/* Admin Dashboard Styles */

/* Custom Button Styles */
.customButton {
  background-color: #eca869;
  border-color: #eca869;
  border: none;
  color: white;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(236, 168, 105, 0.3);
  transition: all 0.2s ease-in-out;
  margin-top: 20px;
}

.customButton:hover {
  background-color: #d4956b !important;
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(236, 168, 105, 0.4);
  border-color: #d4956b !important;
}

.customButton:focus {
  background-color: #d4956b !important;
  border-color: #d4956b !important;
  box-shadow: 0 6px 16px rgba(236, 168, 105, 0.4);
}

/* Export Button Specific Styles */
.exportButton {
  background-color: #00f2fe !important;
  border-color: #00f2fe !important;
  color: white;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(0, 242, 254, 0.3);
  transition: all 0.2s ease-in-out;
  margin-top: 0;
}

.exportButton:hover {
  background-color: #00d9e6 !important;
  border-color: #00d9e6 !important;
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(0, 242, 254, 0.4);
}

.exportButton:focus {
  background-color: #00d9e6 !important;
  border-color: #00d9e6 !important;
  box-shadow: 0 6px 16px rgba(0, 242, 254, 0.4);
}

/* Page Header Styles */
.pageHeader {
  background-color: #061c3d;
  color: white;
  padding: 1rem 1.5rem;
}

.logoText {
  font-size: 1.5rem;
  font-weight: 600;
}

.logoHighlight {
  background-color: #eca869;
  color: black;
  padding: 2px 2px 1px 6px;
  border-radius: 0px 13px;
}

/* Dashboard Cards */
.dashboardCard {
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
  height: 100%;
}

.dashboardCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

/* Stats Cards */
.statsCard {
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  color: white;
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  cursor: default;
  min-height: 170px;
  display: flex;
  align-items: center;
}

.statsCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
}

.statsCardInventory {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.statsCardEquippers {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.statsCardLodgers {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.statsCardBids {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.statsCardEquipments {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.statsCardBody {
  padding: 1.5rem;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
}

.statsCardIcon {
  font-size: 3rem;
  margin-bottom: 0.8rem;
  opacity: 0.9;
}

.statsCardNumber {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0.8rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.statsCardLabel {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  opacity: 0.9;
}

.cardBody {
  padding: 1.5rem;
  text-align: center;
}

.cardIcon {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  color: #eca869;
}

.cardTitle {
  font-size: 1.1rem;
  font-weight: 600;
  color: #061c3d;
  margin-bottom: 0.5rem;
}

.cardDescription {
  color: #6c757d;
  font-size: 0.9rem;
  line-height: 1.4;
}

/* Search and Filter Styles */
.searchContainer {
  position: relative;
}

.searchIcon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
  font-size: 1rem;
  z-index: 2;
}

.searchInput {
  padding-left: 40px;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.searchInput:focus {
  border-color: #eca869;
  box-shadow: 0 0 0 0.2rem rgba(236, 168, 105, 0.25);
}

/* Dropdown Styles */
.dropdownContainer {
  position: relative;
}

.dropdownButton {
  background-color: white;
  border: 1px solid #dee2e6;
  color: #495057;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 0.9rem;
  text-align: left;
  width: 100%;
  transition: all 0.2s ease;
}

.dropdownButton:hover,
.dropdownButton:focus {
  border-color: #eca869;
  box-shadow: 0 0 0 0.2rem rgba(236, 168, 105, 0.25);
}

.dropdownMenu {
  border: none;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.dropdownSearchContainer {
  position: relative;
}

.dropdownSearchIcon {
  position: absolute;
  left: 8px;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
  font-size: 0.9rem;
  z-index: 2;
}

.dropdownSearchInput {
  padding-left: 28px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  font-size: 0.85rem;
  width: 100%;
}

.dropdownSearchInput:focus {
  border-color: #eca869;
  box-shadow: 0 0 0 0.1rem rgba(236, 168, 105, 0.25);
}

.dropdownItem {
  padding: 8px 16px;
  font-size: 0.9rem;
}

.dropdownItemDisabled {
  padding: 8px 16px;
  font-size: 0.9rem;
}

/* Table Styles */
.equipmentTable {
  border-collapse: separate;
  border-spacing: 0;
}

.tableHeader {
  background-color: #eca869;
  color: black;
}

.tableCell {
  padding: 12px;
  border-bottom: 1px solid #dee2e6;
  vertical-align: middle;
}

.tableCellCenter {
  padding: 12px;
  border-bottom: 1px solid #dee2e6;
  vertical-align: middle;
  text-align: center;
}

.mutedText {
  color: #6c757d;
}

.editButton {
  background-color: #28a745;
  border: none;
  color: white;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.editButton:hover {
  background-color: #218838;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.editButtonIcon {
  font-size: 14px;
}

/* Modal Styles */
.modalHeader {
  background-color: #061c3d;
  color: white;
}

.modalTitle {
  font-size: 1.1rem;
  font-weight: 600;
}

.modalBody {
  padding: 0;
  background-color: #f8f9fa;
}

.modalImageContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  background-color: #f8f9fa;
}

.modalImage {
  max-width: 100%;
  max-height: 500px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.modalImageError {
  text-align: center;
  color: #6c757d;
  font-size: 1.1rem;
}

.modalImageErrorIcon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.modalFooter {
  background-color: #f8f9fa;
  border-top: 1px solid #dee2e6;
}
