import { Routes, Route, Navigate } from 'react-router-dom';
import AdminLogin from './Admin_login';
import AdminDashboard from './Admin_dashboard';
import AdminProtectedRoute from './Admin_protected_route';
import MasterEquipmentUpdateModern from './Master_equipment_update_modern';
import MasterEquipmentAdd from './Master_equipment_add';

export default function AdminRoutes() {
  return (
    <Routes>
      <Route path="/login" element={<AdminLogin />} />
      <Route
        path="/dashboard"
        element={
          <AdminProtectedRoute>
            <AdminDashboard />
          </AdminProtectedRoute>
        }
      />
      <Route
        path="/master-equipment/add"
        element={
          <AdminProtectedRoute>
            <MasterEquipmentAdd />
          </AdminProtectedRoute>
        }
      />
      <Route
        path="/master-equipment/:id"
        element={
          <AdminProtectedRoute>
            <MasterEquipmentUpdateModern />
          </AdminProtectedRoute>
        }
      />
      <Route path="/" element={<Navigate to="/admin/dashboard" replace />} />
    </Routes>
  );
}
