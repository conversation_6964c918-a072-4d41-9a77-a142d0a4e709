import { useState, useEffect, useCallback } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Col, Card, But<PERSON>, Alert } from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import axios from 'axios';
import { getCookies } from '../../shared/helpers/Cookies';

const baseURL = import.meta.env.VITE_REACT_APP_BASE_URL || 'http://localhost:5051';

// Simple debounce function
const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// Validation schema - reusing the same validation as the update form
const validationSchema = Yup.object().shape({
  name_en: Yup.string()
    .trim()
    .required('English name is required')
    .min(2, 'English name must be at least 2 characters')
    .max(255, 'English name must be less than 255 characters'),
  name_fr: Yup.string()
    .trim()
    .required('French name is required')
    .min(2, 'French name must be at least 2 characters')
    .max(255, 'French name must be less than 255 characters'),
  brand: Yup.string()
    .max(100, 'Brand must be less than 100 characters'),
  model: Yup.string()
    .max(100, 'Model must be less than 100 characters'),
  image_link: Yup.string()
    .nullable()
});

export default function MasterEquipmentAdd() {
  const navigate = useNavigate();
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [uploading, setUploading] = useState(false);
  const [selectedFile, setSelectedFile] = useState(null);
  const [previewUrl, setPreviewUrl] = useState('');
  const [nameValidation, setNameValidation] = useState({
    nameEN: { checking: false, available: null, message: '' },
    nameFR: { checking: false, available: null, message: '' }
  });

  // Clear validation messages and reset state on component mount
  useEffect(() => {
    // Reset all validation states on page load/reload
    setNameValidation({
      nameEN: { checking: false, available: null, message: '' },
      nameFR: { checking: false, available: null, message: '' }
    });
    setError('');
    setSuccess('');
  }, []);

  // Cleanup preview URL on unmount
  useEffect(() => {
    return () => {
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [previewUrl]);

  // Debounced validation function that only triggers on user interaction
  const debouncedValidation = useCallback(
    debounce((nameEN, nameFR) => {
      if (nameEN.trim() || nameFR.trim()) {
        checkNameAvailability(nameEN, nameFR);
      } else {
        // Clear validation if both fields are empty
        setNameValidation({
          nameEN: { checking: false, available: null, message: '' },
          nameFR: { checking: false, available: null, message: '' }
        });
      }
    }, 500),
    []
  );

  // Function to check name availability
  const checkNameAvailability = async (nameEN, nameFR) => {
    if (!nameEN.trim() && !nameFR.trim()) return;

    setNameValidation(prev => ({
      nameEN: { ...prev.nameEN, checking: true },
      nameFR: { ...prev.nameFR, checking: true }
    }));

    try {
      const token = getCookies('adminToken');
      const baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5051';

      const response = await axios.post(`${baseURL}/admin/master-equipment/check-names`, {
        name_en: nameEN.trim(),
        name_fr: nameFR.trim()
      }, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      setNameValidation({
        nameEN: {
          checking: false,
          available: response.data.name_en_available,
          message: response.data.name_en_available ? '' : 'Equipment with this English name already exists'
        },
        nameFR: {
          checking: false,
          available: response.data.name_fr_available,
          message: response.data.name_fr_available ? '' : 'Equipment with this French name already exists'
        }
      });
    } catch (error) {
      setNameValidation({
        nameEN: { checking: false, available: null, message: '' },
        nameFR: { checking: false, available: null, message: '' }
      });
    }
  };

  // Initial values for new equipment
  const initialValues = {
    name_en: '',
    name_fr: '',
    brand: '',
    model: '',
    image_link: ''
  };

  // Handle file selection
  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (file) {
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
      if (!allowedTypes.includes(file.type)) {
        setError('Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed.');
        return;
      }

      // Validate file size (max 10MB)
      const maxFileSize = 10 * 1024 * 1024; // 10MB
      if (file.size > maxFileSize) {
        setError('File size too large. Maximum size is 10MB.');
        return;
      }

      setSelectedFile(file);
      setError(''); // Clear any previous errors

      // Create preview URL
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
    }
  };

  // Upload image to equipment after creation
  const uploadImage = async (equipmentId) => {
    if (!selectedFile) return null;

    try {
      setUploading(true);
      const token = getCookies('adminToken');

      const formData = new FormData();
      formData.append('file', selectedFile);

      const response = await axios.post(
        `${baseURL}/admin/master-equipment/${equipmentId}/image`,
        formData,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'multipart/form-data'
          }
        }
      );

      return response.data;
    } catch (err) {
      throw new Error(err.response?.data?.error || 'Failed to upload image');
    } finally {
      setUploading(false);
    }
  };

  const handleSubmit = async (values) => {
    try {
      setSaving(true);
      setError('');

      const token = getCookies('adminToken');
      if (!token) {
        setError('Authentication required. Please log in again.');
        return;
      }

      // Validate that an image is selected
      if (!selectedFile) {
        setError('Please select an image for the equipment.');
        return;
      }

      // Step 1: Create the equipment (without image_link in the payload)
      const equipmentData = { ...values };
      delete equipmentData.image_link; // Remove image_link as we'll set it via upload

      const createResponse = await axios.post(`${baseURL}/admin/master-equipment`, equipmentData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const equipmentId = createResponse.data.id;
      if (!equipmentId) {
        throw new Error('Equipment created but ID not returned');
      }

      // Step 2: Upload the image
      await uploadImage(equipmentId);

      setSuccess('Master equipment created successfully! Redirecting to equipment page...');
      setTimeout(() => {
        navigate(`/admin/master-equipment/${equipmentId}`);
      }, 2000);
    } catch (err) {
      if (err.response?.status === 404) {
        setError(`API endpoint not found. Please ensure the backend is running on ${baseURL}`);
      } else {
        setError(err.response?.data?.error || err.message || 'Failed to create equipment');
      }
    } finally {
      setSaving(false);
    }
  };

  return (
    <Container fluid className="py-4" style={{ backgroundColor: '#f8f9fa', minHeight: '100vh' }}>
      <Row className="justify-content-center">
        <Col lg={10} xl={8}>
          {/* Header */}
          <div style={{
            background: 'linear-gradient(135deg, #061C3D 0%, #0B2A5C 100%)',
            borderRadius: '16px',
            padding: '2rem',
            marginBottom: '2rem',
            color: 'white',
            boxShadow: '0 8px 32px rgba(6, 28, 61, 0.3)'
          }}>
            <div className="d-flex justify-content-between align-items-center">
              <div>
                <h2 style={{ margin: 0, fontWeight: '700', fontSize: '1.8rem' }}>
                  ➕ Add New Master Equipment
                </h2>
                <p style={{ margin: '0.5rem 0 0 0', opacity: 0.9, fontSize: '1rem' }}>
                  Create a new equipment entry in the master inventory
                </p>
              </div>
              <Button
                variant="outline-light"
                onClick={() => navigate('/admin/dashboard')}
                style={{
                  borderRadius: '8px',
                  padding: '8px 16px',
                  fontWeight: '500'
                }}
              >
                ← Back to Dashboard
              </Button>
            </div>
          </div>

          {/* Alerts */}
          {error && (
            <Alert variant="danger" className="mb-4" style={{ borderRadius: '12px' }}>
              <strong>Error:</strong> {error}
            </Alert>
          )}
          {success && (
            <Alert variant="success" className="mb-4" style={{ borderRadius: '12px' }}>
              <strong>Success:</strong> {success}
            </Alert>
          )}

          {/* Form */}
          <Formik
            initialValues={initialValues}
            validationSchema={validationSchema}
            onSubmit={handleSubmit}
            validate={() => {
              const errors = {};
              // Custom validation for file upload
              if (!selectedFile) {
                errors.image_upload = 'Equipment image is required';
              }
              return errors;
            }}
          >
            {({ values, errors, touched, handleChange, handleBlur }) => (
              <Form>
                {/* Basic Information */}
                <Col xs={12}>
                  <Card style={{
                    border: 'none',
                    borderRadius: '12px',
                    boxShadow: '0 4px 12px rgba(0,0,0,0.08)',
                    marginBottom: '1.5rem'
                  }}>
                    <Card.Body style={{ padding: '2rem' }}>
                      <h5 style={{
                        fontWeight: '700',
                        color: '#495057',
                        marginBottom: '1.5rem',
                        borderBottom: '2px solid #ECA869',
                        paddingBottom: '0.5rem'
                      }}>
                        📝 Basic Information
                      </h5>
                      <Row>
                        <Col md={6} className="mb-3">
                          <label className="form-label" style={{
                            fontWeight: '600',
                            color: '#495057',
                            marginBottom: '8px',
                            fontSize: '0.9rem'
                          }}>
                            English Name *
                          </label>
                          <input
                            type="text"
                            className={`form-control ${
                              (touched.name_en && errors.name_en) || nameValidation.nameEN.available === false ? 'is-invalid' :
                              nameValidation.nameEN.available === true && values.name_en.trim() ? 'is-valid' : ''
                            }`}
                            name="name_en"
                            value={values.name_en}
                            onChange={(e) => {
                              handleChange(e);
                              // Trigger debounced validation
                              debouncedValidation(e.target.value, values.name_fr);
                            }}
                            onBlur={(e) => {
                              handleBlur(e);
                              e.target.style.borderColor = '#e9ecef';
                              e.target.style.boxShadow = '0 2px 4px rgba(0,0,0,0.05)';
                            }}
                            style={{
                              padding: '12px 16px',
                              border: `2px solid ${
                                nameValidation.nameEN.available === false ? '#dc3545' :
                                nameValidation.nameEN.available === true && values.name_en.trim() ? '#28a745' : '#e9ecef'
                              }`,
                              borderRadius: '8px',
                              fontSize: '0.9rem',
                              transition: 'all 0.2s ease-in-out',
                              boxShadow: '0 2px 4px rgba(0,0,0,0.05)'
                            }}
                            onFocus={(e) => {
                              e.target.style.borderColor = '#ECA869';
                              e.target.style.boxShadow = '0 0 0 3px rgba(236, 168, 105, 0.1)';
                            }}
                          />
                          {nameValidation.nameEN.checking && (
                            <div className="text-muted mt-1" style={{ fontSize: '0.8rem' }}>
                              <i className="fas fa-spinner fa-spin"></i> Checking availability...
                            </div>
                          )}
                          {nameValidation.nameEN.available === false && nameValidation.nameEN.message ? (
                            <div className="invalid-feedback d-block" style={{ fontSize: '0.8rem' }}>
                              {nameValidation.nameEN.message}
                            </div>
                          ) : touched.name_en && errors.name_en && (
                            <div className="invalid-feedback d-block" style={{ fontSize: '0.8rem' }}>
                              {errors.name_en}
                            </div>
                          )}
                          {nameValidation.nameEN.available === true && values.name_en.trim() && !nameValidation.nameEN.checking && (
                            <div className="valid-feedback d-block" style={{ fontSize: '0.8rem' }}>
                              ✓ English name is available
                            </div>
                          )}
                        </Col>
                        <Col md={6} className="mb-3">
                          <label className="form-label" style={{
                            fontWeight: '600',
                            color: '#495057',
                            marginBottom: '8px',
                            fontSize: '0.9rem'
                          }}>
                            French Name *
                          </label>
                          <input
                            type="text"
                            className={`form-control ${
                              (touched.name_fr && errors.name_fr) || nameValidation.nameFR.available === false ? 'is-invalid' :
                              nameValidation.nameFR.available === true && values.name_fr.trim() ? 'is-valid' : ''
                            }`}
                            name="name_fr"
                            value={values.name_fr}
                            onChange={(e) => {
                              handleChange(e);
                              // Trigger debounced validation
                              debouncedValidation(values.name_en, e.target.value);
                            }}
                            onBlur={(e) => {
                              handleBlur(e);
                              e.target.style.borderColor = '#e9ecef';
                              e.target.style.boxShadow = '0 2px 4px rgba(0,0,0,0.05)';
                            }}
                            style={{
                              padding: '12px 16px',
                              border: `2px solid ${
                                nameValidation.nameFR.available === false ? '#dc3545' :
                                nameValidation.nameFR.available === true && values.name_fr.trim() ? '#28a745' : '#e9ecef'
                              }`,
                              borderRadius: '8px',
                              fontSize: '0.9rem',
                              transition: 'all 0.2s ease-in-out',
                              boxShadow: '0 2px 4px rgba(0,0,0,0.05)'
                            }}
                            onFocus={(e) => {
                              e.target.style.borderColor = '#ECA869';
                              e.target.style.boxShadow = '0 0 0 3px rgba(236, 168, 105, 0.1)';
                            }}
                          />
                          {nameValidation.nameFR.checking && (
                            <div className="text-muted mt-1" style={{ fontSize: '0.8rem' }}>
                              <i className="fas fa-spinner fa-spin"></i> Checking availability...
                            </div>
                          )}
                          {nameValidation.nameFR.available === false && nameValidation.nameFR.message ? (
                            <div className="invalid-feedback d-block" style={{ fontSize: '0.8rem' }}>
                              {nameValidation.nameFR.message}
                            </div>
                          ) : touched.name_fr && errors.name_fr && (
                            <div className="invalid-feedback d-block" style={{ fontSize: '0.8rem' }}>
                              {errors.name_fr}
                            </div>
                          )}
                          {nameValidation.nameFR.available === true && values.name_fr.trim() && !nameValidation.nameFR.checking && (
                            <div className="valid-feedback d-block" style={{ fontSize: '0.8rem' }}>
                              ✓ French name is available
                            </div>
                          )}
                        </Col>
                      </Row>
                      <Row>
                        <Col md={6} className="mb-3">
                          <label className="form-label" style={{
                            fontWeight: '600',
                            color: '#495057',
                            marginBottom: '8px',
                            fontSize: '0.9rem'
                          }}>
                            Brand
                          </label>
                          <input
                            type="text"
                            className="form-control"
                            name="brand"
                            value={values.brand}
                            onChange={handleChange}
                            onBlur={(e) => {
                              handleBlur(e);
                              e.target.style.borderColor = '#e9ecef';
                              e.target.style.boxShadow = '0 2px 4px rgba(0,0,0,0.05)';
                            }}
                            style={{
                              padding: '12px 16px',
                              border: '2px solid #e9ecef',
                              borderRadius: '8px',
                              fontSize: '0.9rem',
                              transition: 'all 0.2s ease-in-out',
                              boxShadow: '0 2px 4px rgba(0,0,0,0.05)'
                            }}
                            onFocus={(e) => {
                              e.target.style.borderColor = '#ECA869';
                              e.target.style.boxShadow = '0 0 0 3px rgba(236, 168, 105, 0.1)';
                            }}
                          />
                        </Col>
                        <Col md={6} className="mb-3">
                          <label className="form-label" style={{
                            fontWeight: '600',
                            color: '#495057',
                            marginBottom: '8px',
                            fontSize: '0.9rem'
                          }}>
                            Model
                          </label>
                          <input
                            type="text"
                            className="form-control"
                            name="model"
                            value={values.model}
                            onChange={handleChange}
                            onBlur={(e) => {
                              handleBlur(e);
                              e.target.style.borderColor = '#e9ecef';
                              e.target.style.boxShadow = '0 2px 4px rgba(0,0,0,0.05)';
                            }}
                            style={{
                              padding: '12px 16px',
                              border: '2px solid #e9ecef',
                              borderRadius: '8px',
                              fontSize: '0.9rem',
                              transition: 'all 0.2s ease-in-out',
                              boxShadow: '0 2px 4px rgba(0,0,0,0.05)'
                            }}
                            onFocus={(e) => {
                              e.target.style.borderColor = '#ECA869';
                              e.target.style.boxShadow = '0 0 0 3px rgba(236, 168, 105, 0.1)';
                            }}
                          />
                        </Col>
                      </Row>
                    </Card.Body>
                  </Card>
                </Col>



                {/* Image Upload */}
                <Col xs={12}>
                  <Card style={{
                    border: 'none',
                    borderRadius: '12px',
                    boxShadow: '0 4px 12px rgba(0,0,0,0.08)',
                    marginBottom: '1.5rem'
                  }}>
                    <Card.Body style={{ padding: '2rem' }}>
                      <h5 style={{
                        fontWeight: '700',
                        color: '#495057',
                        marginBottom: '1.5rem',
                        borderBottom: '2px solid #ECA869',
                        paddingBottom: '0.5rem'
                      }}>
                        📷 Equipment Image *
                      </h5>
                      <Row>
                        <Col md={6} className="mb-3">
                          <label className="form-label" style={{
                            fontWeight: '600',
                            color: '#495057',
                            marginBottom: '8px',
                            fontSize: '0.9rem'
                          }}>
                            Choose Image File *
                          </label>
                          <div style={{ position: 'relative' }}>
                            <input
                              type="file"
                              accept="image/*"
                              onChange={(e) => handleFileSelect(e)}
                              style={{ display: 'none' }}
                              id="image-upload"
                            />
                            <label
                              htmlFor="image-upload"
                              style={{
                                display: 'inline-flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                gap: '8px',
                                padding: '12px 20px',
                                backgroundColor: selectedFile ? '#28a745' : '#ECA869',
                                color: 'white',
                                border: 'none',
                                borderRadius: '8px',
                                cursor: 'pointer',
                                fontWeight: '600',
                                fontSize: '0.9rem',
                                transition: 'all 0.2s ease-in-out',
                                boxShadow: '0 4px 12px rgba(236, 168, 105, 0.3)',
                                width: '100%'
                              }}
                              onMouseEnter={(e) => {
                                if (!selectedFile) {
                                  e.target.style.backgroundColor = '#d4956b';
                                  e.target.style.transform = 'translateY(-1px)';
                                }
                              }}
                              onMouseLeave={(e) => {
                                if (!selectedFile) {
                                  e.target.style.backgroundColor = '#ECA869';
                                  e.target.style.transform = 'translateY(0)';
                                }
                              }}
                              onFocus={(e) => {
                                e.target.style.outline = '2px solid #ECA869';
                                e.target.style.outlineOffset = '2px';
                              }}
                              onBlur={(e) => {
                                e.target.style.outline = 'none';
                              }}
                            >
                              {selectedFile ? '✅ Image Selected' : '📁 Choose Image'}
                            </label>
                          </div>
                          {selectedFile && (
                            <div style={{
                              marginTop: '8px',
                              padding: '8px 12px',
                              backgroundColor: '#e8f5e8',
                              borderRadius: '6px',
                              fontSize: '0.8rem',
                              color: '#155724'
                            }}>
                              <strong>Selected:</strong> {selectedFile.name}
                              <br />
                              <strong>Size:</strong> {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                            </div>
                          )}
                          {errors.image_upload && (
                            <div className="invalid-feedback d-block" style={{ fontSize: '0.8rem' }}>
                              {errors.image_upload}
                            </div>
                          )}
                        </Col>
                        <Col md={6} className="mb-3">
                          <label className="form-label" style={{
                            fontWeight: '600',
                            color: '#495057',
                            marginBottom: '8px',
                            fontSize: '0.9rem'
                          }}>
                            Image Preview
                          </label>
                          <div style={{
                            width: '100%',
                            height: '200px',
                            border: '2px dashed #e9ecef',
                            borderRadius: '8px',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            backgroundColor: '#f8f9fa',
                            overflow: 'hidden'
                          }}>
                            {previewUrl ? (
                              <img
                                src={previewUrl}
                                alt="Equipment preview"
                                style={{
                                  maxWidth: '100%',
                                  maxHeight: '100%',
                                  objectFit: 'contain',
                                  borderRadius: '6px'
                                }}
                              />
                            ) : (
                              <div style={{
                                textAlign: 'center',
                                color: '#6c757d',
                                fontSize: '0.9rem'
                              }}>
                                <div style={{ fontSize: '2rem', marginBottom: '8px' }}>📷</div>
                                <div>No image selected</div>
                                <div style={{ fontSize: '0.8rem', marginTop: '4px' }}>
                                  JPEG, PNG, GIF, WebP (max 10MB)
                                </div>
                              </div>
                            )}
                          </div>
                        </Col>
                      </Row>
                    </Card.Body>
                  </Card>
                </Col>

                {/* Action Buttons */}
                <Col xs={12}>
                  <div className="d-flex justify-content-end gap-3 mt-4">
                    <Button
                      type="button"
                      variant="outline-secondary"
                      onClick={() => navigate('/admin/dashboard')}
                      style={{
                        padding: '12px 24px',
                        borderRadius: '8px',
                        fontWeight: '500',
                        border: '2px solid #6c757d'
                      }}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      disabled={saving || uploading}
                      style={{
                        backgroundColor: '#ECA869',
                        borderColor: '#ECA869',
                        color: 'white',
                        padding: '12px 24px',
                        borderRadius: '8px',
                        fontWeight: '600',
                        border: 'none',
                        boxShadow: '0 4px 12px rgba(236, 168, 105, 0.3)'
                      }}
                    >
                      {saving ? (uploading ? 'Uploading Image...' : 'Creating Equipment...') : '✅ Create Equipment'}
                    </Button>
                  </div>
                </Col>
              </Form>
            )}
          </Formik>
        </Col>
      </Row>
    </Container>
  );
}
