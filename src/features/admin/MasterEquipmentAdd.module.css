/* Master Equipment Add Styles */

.container {
  background-color: #f8f9fa;
  min-height: 100vh;
  padding: 1.5rem 0;
}

.header {
  background: linear-gradient(135deg, #061C3D 0%, #0B2A5C 100%);
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 2rem;
  color: white;
  box-shadow: 0 8px 32px rgba(6, 28, 61, 0.3);
}

.headerContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.headerTitle {
  margin: 0;
  font-weight: 700;
  font-size: 1.8rem;
}

.headerSubtitle {
  margin: 0.5rem 0 0 0;
  opacity: 0.9;
  font-size: 1rem;
}

.backButton {
  border-radius: 8px;
  padding: 8px 16px;
  font-weight: 500;
}

.alert {
  border-radius: 12px;
  margin-bottom: 1.5rem;
}

.card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.08);
  margin-bottom: 1.5rem;
}

.cardBody {
  padding: 2rem;
}

.sectionTitle {
  font-weight: 700;
  color: #495057;
  margin-bottom: 1.5rem;
  border-bottom: 2px solid #ECA869;
  padding-bottom: 0.5rem;
}

.formLabel {
  font-weight: 600;
  color: #495057;
  margin-bottom: 8px;
  font-size: 0.9rem;
}

.formControl {
  padding: 12px 16px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 0.9rem;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.formControl:focus {
  border-color: #ECA869;
  box-shadow: 0 0 0 3px rgba(236, 168, 105, 0.1);
  outline: none;
}

.formControl:not(:focus) {
  border-color: #e9ecef;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.formControl.isInvalid {
  border-color: #dc3545;
}

.formControl.isValid {
  border-color: #28a745;
}

.validationMessage {
  font-size: 0.8rem;
  margin-top: 4px;
}

.validationSpinner {
  color: #6c757d;
}

.validationSpinner .spinner {
  width: 12px;
  height: 12px;
  margin-right: 8px;
}

.validFeedback {
  color: #28a745;
}

.invalidFeedback {
  color: #dc3545;
}

.fileUploadContainer {
  position: relative;
}

.fileUploadInput {
  display: none;
}

.fileUploadLabel {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 20px;
  background-color: #ECA869;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  font-size: 0.9rem;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 4px 12px rgba(236, 168, 105, 0.3);
  width: 100%;
  text-decoration: none;
}

.fileUploadLabel:hover {
  background-color: #d4956b;
  transform: translateY(-1px);
  color: white;
}

.fileUploadLabel:focus {
  outline: 2px solid #ECA869;
  outline-offset: 2px;
}

.fileUploadLabel.selected {
  background-color: #28a745;
}

.fileUploadLabel.selected:hover {
  background-color: #28a745;
  transform: none;
}

.fileInfo {
  margin-top: 8px;
  padding: 8px 12px;
  background-color: #e8f5e8;
  border-radius: 6px;
  font-size: 0.8rem;
  color: #155724;
}

.imagePreviewContainer {
  width: 100%;
  height: 200px;
  border: 2px dashed #e9ecef;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  overflow: hidden;
}

.imagePreview {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 6px;
}

.imagePlaceholder {
  text-align: center;
  color: #6c757d;
  font-size: 0.9rem;
}

.imagePlaceholderIcon {
  font-size: 2rem;
  margin-bottom: 8px;
}

.imagePlaceholderSubtext {
  font-size: 0.8rem;
  margin-top: 4px;
}

.actionButtons {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
}

.cancelButton {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  border: 2px solid #6c757d;
}

.submitButton {
  background-color: #ECA869;
  border-color: #ECA869;
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  border: none;
  box-shadow: 0 4px 12px rgba(236, 168, 105, 0.3);
}

.submitButton:hover {
  background-color: #d4956b;
  border-color: #d4956b;
}

.submitButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
