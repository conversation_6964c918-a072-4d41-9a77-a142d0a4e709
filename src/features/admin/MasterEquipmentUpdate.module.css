/* Master Equipment Update Styles */

.pageHeader {
  width: 100%;
  background-color: #061C3D;
  color: white;
  padding: 1rem 1.5rem;
}

.headerContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logoSection {
  display: flex;
  align-items: center;
  margin-right: 2rem;
}

.logoText {
  font-size: 1.5rem;
  font-weight: 600;
}

.logoHighlight {
  background-color: #ECA869;
  padding: 2px 2px 1px 6px;
  border-radius: 0px 13px;
  color: black;
}

.pageTitle {
  margin: 0;
  font-weight: 600;
}

.userInfo {
  display: flex;
  align-items: center;
}

.userDetails {
  margin-right: 1rem;
}

.userLabel {
  font-size: 0.9rem;
  opacity: 0.9;
}

.userEmail {
  font-weight: 500;
}

.backButton {
  font-weight: 500;
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.08);
  margin-bottom: 1.5rem;
}

.cardBody {
  padding: 1.5rem;
}

.sectionTitle {
  font-weight: 600;
  color: #495057;
  margin-bottom: 1.5rem;
  border-bottom: 2px solid #ECA869;
  padding-bottom: 0.5rem;
}

.formLabel {
  font-weight: 600;
  color: #495057;
  margin-bottom: 8px;
  font-size: 0.9rem;
}

.formControl {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 0.9rem;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  display: block;
}

.formControl:focus {
  border-color: #ECA869 !important;
  box-shadow: 0 0 0 3px rgba(236, 168, 105, 0.1) !important;
  outline: none;
}

.formControl:not(:focus) {
  border-color: #e9ecef;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.formControl.isInvalid:not(:focus),
.isInvalid:not(:focus) {
  border-color: #dc3545 !important;
}

.formControl.isValid:not(:focus),
.isValid:not(:focus) {
  border-color: #28a745 !important;
}

.readOnlyField {
  background-color: #f8f9fa !important;
  color: #6c757d !important;
  cursor: not-allowed !important;
  border-color: #e9ecef !important;
}

.textareaControl {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 0.9rem;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  resize: vertical;
  display: block;
}

.textareaControl:focus {
  border-color: #ECA869 !important;
  box-shadow: 0 0 0 3px rgba(236, 168, 105, 0.1) !important;
  outline: none;
}

.textareaControl:not(:focus) {
  border-color: #e9ecef;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.validationSpinner {
  display: flex;
  align-items: center;
  margin-top: 4px;
  font-size: 0.8rem;
  color: #6c757d;
}

.validationSpinnerIcon {
  width: 12px;
  height: 12px;
  margin-right: 8px;
}

.validFeedback {
  font-size: 0.8rem;
  color: #28a745;
  margin-top: 4px;
}

.invalidFeedback {
  font-size: 0.8rem;
  color: #dc3545;
  margin-top: 4px;
}

.imageUploadSection {
  font-weight: 600;
  color: #495057;
  margin-bottom: 1rem;
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 0.5rem;
}

.imageUploadControls {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 1rem;
}

.imageUploadInput {
  display: none;
}

.imageUploadLabel {
  display: inline-block;
  background-color: transparent;
  color: #007bff;
  border: 2px solid #007bff;
  border-radius: 8px;
  font-weight: 500;
  padding: 8px 16px;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  text-decoration: none;
}

.imageUploadLabel:hover {
  background-color: #007bff;
  color: white;
}

.imageUploadLabel:focus {
  background-color: #007bff;
  color: white;
}

.imageUploadLabel:not(:hover):not(:focus) {
  background-color: transparent;
  color: #007bff;
}

.uploadButton {
  border-radius: 8px;
  font-weight: 500;
  padding: 8px 16px;
}

.imagePreviewContainer {
  display: flex;
  gap: 12px;
  align-items: start;
}

.imagePreviewSection {
  text-align: center;
}

.imagePreviewLabel {
  font-weight: 500;
  color: #6c757d;
  font-size: 0.8rem;
  display: block;
  margin-bottom: 8px;
}

.imagePreview {
  width: 100px;
  height: 100px;
  object-fit: cover;
  border-radius: 8px;
  border: 2px solid #28a745;
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
}

.currentImage {
  width: 100px;
  height: 100px;
  object-fit: cover;
  border-radius: 8px;
  border: 2px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.classificationSection {
  border-top: 1px solid #e9ecef;
  padding-top: 1.5rem;
  margin-top: 1.5rem;
}

.classificationHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.classificationTitle {
  font-weight: 600;
  color: #495057;
  margin: 0;
}

.classifyButton {
  background-color: #17a2b8;
  border-color: #17a2b8;
  color: white;
  border-radius: 8px;
  font-weight: 500;
  padding: 8px 16px;
  border: none;
}

.classifyButton:hover {
  background-color: #138496;
  border-color: #117a8b;
}

.classifyButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.classificationInfo {
  background-color: #e8f4f8;
  border: 1px solid #bee5eb;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.classificationInfoText {
  margin: 0;
  color: #0c5460;
  font-size: 0.9rem;
}

.actionButtons {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
}

.cancelButton {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  border: 2px solid #6c757d;
}

.submitButton {
  background-color: #ECA869;
  border-color: #ECA869;
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  border: none;
  box-shadow: 0 4px 12px rgba(236, 168, 105, 0.3);
}

.submitButton:hover {
  background-color: #d4956b;
  border-color: #d4956b;
}

.submitButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.generateButtonContainer {
  margin-top: 4px;
  margin-bottom: 4px;
}
