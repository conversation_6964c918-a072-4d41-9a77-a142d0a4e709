//go:generate mockery --name=Database --output=./mocks --outpkg=mocks --case=underscore
package db

import (
	"context"

	"github.com/vima-inc/derental/models"
)

// Database represents the database.
type Database interface {
	// Lodger.
	// GetLodgerByID returns a lodger by id.
	GetLodgerByID(ctx context.Context, id string) (models.Lodger, error)
	// Add<PERSON>od<PERSON> adds a new lodger.
	AddLodger(ctx context.Context, lodger models.Lodger) error
	// UpdateLodger updates an existing lodger.
	UpdateLodger(ctx context.Context, lodger models.Lodger) error
	// DeleteLodger deletes an existing lodger by id.
	DeleteLodger(ctx context.Context, id string) error
	// GetAll<PERSON>odgers fetch all the lodgers.
	GetAllLodgers(ctx context.Context) ([]models.Lodger, error)
	//// GetAllLodgersByCountry fetch all the lodgers of the same country.
	GetAllLodgersByCountry(ctx context.Context, country string) ([]models.Lodger, error)
	// GetLodgerByMemberID returns a lodger by member id.
	GetLodgerByMemberID(ctx context.Context, memberID string) (models.Lodger, error)

	// Equipper.
	// CheckEquipperByUserName checks if an equipper exists by userName.
	CheckEquipperByUserName(ctx context.Context, userName string) (bool, error)
	// GetEquipperByUserName returns an equipper by userName.
	GetEquipperByUserName(ctx context.Context, userName string) (models.Equipper, error)
	// GetEquipperByID returns an equipper by id.
	GetEquipperByID(ctx context.Context, id string) (models.Equipper, error)
	// GetEquipperByEmail returns a equipper by email.
	GetEquipperByEmail(ctx context.Context, email string) (models.Equipper, error)
	// GetAllEquipper returns all equipper.
	GetAllEquipper(ctx context.Context) ([]models.Equipper, error)
	// AddEquipper adds a new equipper.
	AddEquipper(ctx context.Context, equipper models.Equipper) error
	// UpdateEquipper updates an existing equipper.
	UpdateEquipper(ctx context.Context, equipper models.Equipper) error
	// DeleteEquipper deletes an existing equipper by id.
	DeleteEquipper(ctx context.Context, id string) error
	// GetAllEquippersByCategory returns all equipper by category.
	GetAllEquippersByCategory(ctx context.Context, category []string) ([]models.Equipper, error)
	// GetAllEquippersByCategoryCoverageArea returns all equipper by category && coverage area.
	GetAllEquippersByCategoryCoverageArea(ctx context.Context, category []string, CoverageArea []string) ([]models.Equipper, error)
	// GetEquipperByMemberID returns an equipper by member id.
	GetEquipperByMemberID(ctx context.Context, memberID string) (models.Equipper, error)
	// GetEquippersByCountry returns all equippers by country.
	GetEquippersByCountry(ctx context.Context, countryCode string) ([]models.Equipper, error)

	// Members.
	// GetMembers returns lodger members.
	GetMembers(ctx context.Context, lodgerID string) (models.Members, error)
	// AddMember add a new member.
	AddMember(ctx context.Context, lodgerID string, member models.Member) (models.Member, error)
	// UpdateMember updates an existing member.
	UpdateMember(ctx context.Context, lodgerID string, member models.Member) error
	// DeleteMember deletes an existing member by ids.
	DeleteMember(ctx context.Context, lodgerID string, memberID ...string) error
	// GetMemberByID returns a member by id.
	GetMemberByID(ctx context.Context, memberID string, lodgerID string) (models.Member, error)
	// GetSelectionList returns a selection list of members.
	GetSelectionList(ctx context.Context, member models.Member) ([]models.Member, error)
	// invitationExists checks if an invitation exists.
	InvitationExists(ctx context.Context, email string, memberOf string) (bool, error)
	// GetAdminsByOwnerID returns admins by owner id.
	GetAdminsByOwnerID(ctx context.Context, ownerID string) ([]models.Member, error)

	// Equipments
	// GetEquipmentByID returns a equipment by id.
	GetEquipmentByID(ctx context.Context, id string) (models.Equipment, error)
	// GetAllEquipmentsByEquipperID returns equipments by equipper id.
	GetAllEquipmentsByEquipperID(ctx context.Context, equipperID string) ([]models.Equipment, error)
	// GetEquipmentsByEquipperID returns equipments by equipper id.
	GetEquipmentsByEquipperID(ctx context.Context, equipperID string, limit int, lastID string) ([]models.Equipment, error)
	// GetBookedEquipmentsByEquipperID returns booked equipments by equipper id.
	GetBookedEquipmentsByEquipperID(ctx context.Context, equipperID string, limit int, lastID string) ([]models.Equipment, error)
	// GetBookedEquipmentsByEquipperID returns booked equipments by equipper id.
	GetAllBookedEquipmentsByEquipperID(ctx context.Context, equipperID string) ([]models.Equipment, error)
	// GetAvailableEquipmentsByEquipperIDAndEquipmentName returns available equipments by equipper id & equipment name.
	GetAvailableEquipmentsByEquipperIDAndEquipmentName(ctx context.Context, equipperID string, equipmentName string) ([]models.Equipment, error)
	// GetAvailableEquipmentsByEquipperIDAndAliasEN returns available equipments by equipper id & equipment name.
	GetAvailableEquipmentsByEquipperIDAndAliasEN(ctx context.Context, equipperID string, equipmentName string) ([]models.Equipment, error)
	// GetAvailableEquipmentsByEquipperIDAndAliasFR returns available equipments by equipper id & equipment name.
	GetAvailableEquipmentsByEquipperIDAndAliasFR(ctx context.Context, equipperID string, equipmentName string) ([]models.Equipment, error)
	// AddEquipment adds a new equipment.
	AddEquipment(ctx context.Context, equipment models.Equipment) (models.Equipment, error)
	// BatchAddEquipment adds equipments in batch.
	BatchAddEquipment(ctx context.Context, equipments []models.Equipment) error
	// BatchUpdateEquipment updates equipments in batch.
	BatchUpdateEquipment(ctx context.Context, equipments []models.Equipment) error
	// UpdateEquipment updates an existing equipment.
	UpdateEquipment(ctx context.Context, equipment models.Equipment) error
	// DeleteEquipment deletes an existing equipment by id.
	DeleteEquipment(ctx context.Context, id string) error
	// GetEquipmentByName get equipment by name.
	GetEquipmentByName(ctx context.Context, name string) ([]models.Equipment, error)
	// DeleteEquipmentsByEquipperID deletes equipments by equipper id.
	DeleteEquipmentsByEquipperID(ctx context.Context, equipperID string) error
	// ChangeEquipmentStatus changes equipment status.
	ChangeEquipmentStatus(ctx context.Context, equipmentID string, status models.EquipmentStatus) error
	// CountAllEquipments returns the total count of all equipments.
	CountAllEquipments(ctx context.Context) (int, error)

	// Project.
	// GetProjectByLodgerID returns a project by lodger id.
	GetProjectByLodgerID(ctx context.Context, lodgerID string) ([]models.Project, error)

	// GetProjectByEquipperID returns a project by member id.
	GetProjectByMemberID(ctx context.Context, memberID string) ([]models.Project, error)
	// GetProjectByID returns a project by id.
	GetProjectByID(ctx context.Context, id string) (models.Project, error)

	// GetProjectByCreditApplicationID returns a project by credit application id .
	GetProjectByCreditApplicationID(ctx context.Context, ccaID string) ([]models.Project, error)

	// AddProject adds a new project.
	AddProject(ctx context.Context, project models.Project) (string, error)
	// UpdateProject updates an existing project.
	UpdateProject(ctx context.Context, project models.Project) error
	// DeleteProject deletes an existing project by id.
	DeleteProject(ctx context.Context, id string) error
	// PopulateProject populates project gets project id and returns a PopulatedProject.
	PopulateProject(ctx context.Context, projectID string) (models.PopulatedProject, error)
	// GetProjectByEquipmentID returns a projects by equipment id.
	GetProjectsByEquipmentID(ctx context.Context, equipmentID string) ([]models.Project, error)
	// BatchUpdateProjects updates projects in batch.
	BatchUpdateProjects(ctx context.Context, projects []models.Project) error
	// GetProjectsByCCFID returns a projects by ccf id.
	GetProjectsByCCFID(ctx context.Context, ccfID string) ([]models.Project, error)

	// AddPasswordReset adds a new password reset.
	AddPasswordReset(ctx context.Context, passwordReset models.PasswordReset) error
	// GetPasswordReset returns a password reset by email.
	GetPasswordReset(ctx context.Context, email string) (models.PasswordReset, error)
	// PasswordReset deletes a password reset.
	DeletePasswordReset(ctx context.Context, id string) error

	// GetLodgerByEmail returns a lodger by email.
	GetLodgerByEmail(ctx context.Context, email string) (models.Lodger, error)
	// Lead.
	// AddLead adds a new lead.
	AddLead(ctx context.Context, lead models.Lead) error

	// BookEquipment.
	// AddBookEquipment adds a new booking request.
	AddBookEquipment(ctx context.Context, bookEquipment models.BookEquipment) (string, error)
	// GetBookEquipmentByID returns a booking equipment by id.
	GetBookEquipmentByID(ctx context.Context, id string) (models.BookEquipment, error)
	// GetBookEquipmentByEquipperID returns a booking request by equipper id.
	GetBookEquipmentByEquipperID(ctx context.Context, equipperID string) ([]models.BookEquipment, error)
	// GetAcceptedBookingByEquipperID returns a accepted book_equipments by equipper id.
	GetBookedByStatusAndEquipperID(ctx context.Context, equipperID string, status models.BookingStatus, limit int, lastID string) ([]models.BookEquipment, error)
	// GetBookEquipmentByLodgerID returns a booking request by lodger id.
	GetBookEquipmentByLodgerID(ctx context.Context, lodgerID string) ([]models.BookEquipment, error)
	// GetBookEquipmentByAdminIDs returns a booking request by admin ids.
	GetBookEquipmentByAdminIDs(ctx context.Context, adminID string, status models.BookingStatus) ([]models.BookEquipment, error)
	// GetBookEquipmentByOwnerID returns a booking request by owner id.
	GetBookEquipmentByOwnerID(ctx context.Context, ownerID string, status models.BookingStatus) ([]models.BookEquipment, error)
	// GetInProgressBookingByLodgerID returns a accepted book_equipments by lodger id.
	GetBookedEquipmentByStatusAndLodgerID(ctx context.Context, equipperID string, status models.BookingStatus, limit int, lastID string) ([]models.BookEquipment, error)
	// DeleteBookEquipment deletes an existing booking request by id.
	DeleteBookEquipment(ctx context.Context, id string) error
	// UpdateBookEquipment updates an existing booking request.
	UpdateBookEquipment(ctx context.Context, bookingRequest models.BookEquipment) error
	// GetBookEquipmentByEquipmentID returns a booking request by equipment id.
	GetBookEquipmentByEquipmentID(ctx context.Context, equipmentID string) ([]models.BookEquipment, error)
	// GetAcceptedBookEquipmentByEquipmentID returns a accepted booking request by equipment id.
	GetAcceptedBookEquipmentByEquipmentID(ctx context.Context, equipmentID string) (models.BookEquipment, error)
	// GetBookingRequestsByCCFID returns a booking request by ccf id.
	GetBookingRequestsByCCFID(ctx context.Context, ccfID string) ([]models.BookEquipment, error)
	// BatchUpdateBookingRequests updates booking requests in batch.
	BatchUpdateBookingRequests(ctx context.Context, bookingRequests []models.BookEquipment) error
	// GetBookEquipmentByStatusAndEndDate returns a book_equipments by status accepted and EndDate <= time.now
	GetBookEquipmentByStatusAndEndDate(ctx context.Context) ([]models.BookEquipment, error)

	// Bids request.
	// AddBidsRequest add a new Bids Request.
	AddBidsRequest(ctx context.Context, lodgerID string, bidsRequest models.BidsRequest) error
	// UpdateRequest updates an existing Bids request.
	UpdateRequest(ctx context.Context, bidsRequest models.BidsRequest) error
	// GetAllLodgerBidsRequest get list of all bids request by lodger id.
	GetAllLodgerBidsRequest(ctx context.Context, limit int, lastID string, status models.BidsRequestStatus, lodgerID string) ([]models.BidsRequest, error)
	// GetAllBidsRequest get list of all bids request.
	GetAllBidsRequest(ctx context.Context, limit int, lastID string, status models.BidsRequestStatus, equipperID string) ([]models.BidsRequest, error)
	// GetBidsRequestID returns bids request  id.
	GetBidsRequestID(ctx context.Context, id string) (models.BidsRequest, error)
	// GetBidsRequestByEquipmentID returns bids offer by equipment id.
	GetBidsRequestByEquipmentID(ctx context.Context, equipmentID string) ([]models.BidsRequest, error)
	// GetAllOwnerBidsRequest get list of all bids request by owner id.
	GetAllOwnerBidsRequest(ctx context.Context, ownerID string, limit int, lastID string, status models.BidsRequestStatus) ([]models.BidsRequest, error)
	// GetAllAdminsBidsRequest get list of all bids request by admin id.
	GetAllAdminsBidsRequest(ctx context.Context, adminID string, limit int, lastID string, status models.BidsRequestStatus) ([]models.BidsRequest, error)

	// Bids Offer.
	// AddOfferRequest add a new Bids offer.
	AddOfferRequest(ctx context.Context, equipperID string, bidsOffer models.BidzOffer) error
	// GetAllBidsLodgerOffer get list of all lodger bids offers.
	GetAllBidsLodgerOffer(ctx context.Context, limit int, lastID string, status models.BidsOfferStatus, lodgerID string) ([]models.BidzOffer, error)
	// GetAllBidsOffer get list of all bids offers.
	GetAllBidsOffer(ctx context.Context, limit int, lastID string, status models.BidsOfferStatus, where map[string]interface{}) ([]models.BidzOffer, error)
	// GetBidsOfferID returns bids offer  id.
	GetBidsOfferID(ctx context.Context, id string) (models.BidzOffer, error)
	// GetBidsOfferByEquipmentID returns bids offer by equipment id.
	GetBidsOfferByEquipmentID(ctx context.Context, equipmentID string) ([]models.BidzOffer, error)
	// UpdateOffer updates an existing Bids offer.
	UpdateOffer(ctx context.Context, bidsOffer models.BidzOffer) error
	// GetAllOwnerBidsOffer get list of all bids offer by owner id.
	GetAllOwnerBidsOffer(ctx context.Context, ownerID string, limit int, lastID string, status models.BidsOfferStatus) ([]models.BidzOffer, error)
	// GetAllAdminsBidsOffer get list of all bids offer by admin id.
	GetAllAdminsBidsOffer(ctx context.Context, adminID string, limit int, lastID string, status models.BidsOfferStatus) ([]models.BidzOffer, error)

	// CreditCheckForm.
	// AddCreditCheckForm adds a new credit check form.
	AddCreditCheckForm(ctx context.Context, creditCheckForm models.CreditCheckForm) (models.CreditCheckForm, error)
	// GetCreditCheckFormByID returns a credit check form by id.
	GetCreditCheckFormByID(ctx context.Context, id string) (models.CreditCheckForm, error)
	// UpdateCreditCheckForm updates an existing credit check form.
	UpdateCreditCheckForm(ctx context.Context, creditCheckForm models.CreditCheckForm) error
	// DeleteCreditCheckForm deletes an existing credit check form by id.
	DeleteCreditCheckForm(ctx context.Context, id string) error
	// GetCreditCheckFormByLodgerID returns a credit check form by lodger id.
	GetCreditCheckFormByLodgerID(ctx context.Context, lodgerID string) ([]models.CreditCheckForm, error)

	// ToolerBidzEquipmentInventory.
	// AddToolerBidzEquipment adds a new tooler bidz equipment inventory.
	AddToolerBidzEquipment(ctx context.Context, toolerBidzEquipmentInventory models.ToolerBidzEquipment) (models.ToolerBidzEquipment, error)
	// UpdateToolerBidzEquipment updates an existing tooler bidz equipment inventory.
	UpdateToolerBidzEquipment(ctx context.Context, toolerBidzEquipmentInventory models.ToolerBidzEquipment) error
	// DeleteToolerBidzEquipment deletes an existing tooler bidz equipment inventory by id.
	DeleteToolerBidzEquipment(ctx context.Context, id string) error
	// GetAllToolerBidzEquipment returns a list of all tooler bidz equipment inventory.
	GetAllToolerBidzEquipment(ctx context.Context) ([]models.ToolerBidzEquipment, error)
	// GetToolerBidzEquipmentByID returns a tooler bidz equipment inventory by id.
	GetToolerBidzEquipmentByID(ctx context.Context, id string) (models.ToolerBidzEquipment, error)
	// GetEquipmentInventoryByName return equipment inventory by name.
	GetEquipmentInventoryByName(ctx context.Context, name string) (models.ToolerBidzEquipment, error)
	// GetEquipmentInventoryByNameFR return equipment inventory by French name.
	GetEquipmentInventoryByNameFR(ctx context.Context, name string) (models.ToolerBidzEquipment, error)
	// DropToolerBidzEquipments drops a tooler bidz equipment inventory.
	DropToolerBidzEquipments(ctx context.Context) error

	// Orders.
	// AddOrder adds an order to the database.
	AddOrder(ctx context.Context, order models.Order) error
	// GetOrderByID returns an order by id.
	GetOrderByID(ctx context.Context, id string) (models.Order, error)
	// GetOrderByBookingID returns an order by booking id.
	GetOrderByBookingID(ctx context.Context, id string) (models.Order, error)
	// GetOrderByPaymentIntentID returns an order by payment intent id.
	GetOrderByPaymentIntentID(ctx context.Context, paymentIntentID string) (models.Order, error)
	// UpdateOrder updates an order in the database.
	UpdateOrder(ctx context.Context, order models.Order) error

	// Promotions.
	// CreatePromotionCode creates a new promotion code.
	CreatePromotionCode(ctx context.Context, promo models.Promotion) error
	// DeletePromotionCode deletes a promotion code.
	DeletePromotionCode(ctx context.Context, equipperID string, code string) error
	// GetAllPromotionCodeByEquipperID gets all promotion codes for an equipper.
	GetAllPromotionCodeByEquipperID(ctx context.Context, equipperID string) ([]models.Promotion, error)
	// GetPromotionCodeByEquipperIDAndLodgerID gets a promotion code for an equipper and lodger.
	GetPromotionCodeByEquipperIDAndLodgerID(ctx context.Context, equipperID string, lodgerID string) (models.Promotion, error)
	GetEquipmentByInternalID(ctx context.Context, id string) (models.Equipment, error)

	// GetFirestoreClient returns the underlying Firestore client for advanced operations
	GetFirestoreClient() interface{}
}
