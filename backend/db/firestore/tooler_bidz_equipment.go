package firestoredb

import (
	"context"
	"errors"
	"fmt"

	"google.golang.org/api/iterator"

	"github.com/vima-inc/derental/models"
)

const toolerBidzEquipmentCollectionName = "tooler_bidz_equipment_inventory"

// AddToolerBidzEquipment adds equipment bidz.
func (f *firestoredb) AddToolerBidzEquipment(ctx context.Context, toolerBidzEquipment models.ToolerBidzEquipment) (models.ToolerBidzEquipment, error) {
	newDoc := f.client.Collection(toolerBidzEquipmentCollectionName).NewDoc()
	toolerBidzEquipment.ID = newDoc.ID

	_, err := newDoc.Set(ctx, toolerBidzEquipment)
	if err != nil {
		return models.ToolerBidzEquipment{}, fmt.Errorf("unable to set tooler bidz equipment with id: %s error: %w", toolerBidzEquipment.ID, err)
	}

	return toolerBidzEquipment, nil
}

// UpdateToolerBidzEquipment updates the tooler bidz equipment.
func (f *firestoredb) UpdateToolerBidzEquipment(ctx context.Context, toolerBidzEquipment models.ToolerBidzEquipment) error {
	_, err := f.client.Collection(toolerBidzEquipmentCollectionName).Doc(toolerBidzEquipment.ID).Set(ctx, toolerBidzEquipment)
	if err != nil {
		return fmt.Errorf("unable to set tooler bidz equipment with id: %w", err)
	}

	return nil
}

// DeleteToolerBidzEquipment deletes the bidz equipment.
func (f *firestoredb) DeleteToolerBidzEquipment(ctx context.Context, id string) error {
	_, err := f.client.Collection(toolerBidzEquipmentCollectionName).Doc(id).Delete(ctx)
	if err != nil {
		return fmt.Errorf("unable to delete tooler bidz equipment: %w", err)
	}

	return nil
}

// GetAllToolerBidzEquipment returns all the tooler bidz equipments.
func (f *firestoredb) GetAllToolerBidzEquipment(ctx context.Context) ([]models.ToolerBidzEquipment, error) {
	var toolerBidzEquipment []models.ToolerBidzEquipment

	iter := f.client.Collection(toolerBidzEquipmentCollectionName).Documents(ctx)

	for {
		doc, err := iter.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return nil, fmt.Errorf("unable to iterate over tooler bidz equipment: %w", err)
		}

		var toolerBidzEquipmentInventory models.ToolerBidzEquipment

		err = doc.DataTo(&toolerBidzEquipmentInventory)
		if err != nil {
			return nil, fmt.Errorf("unable to get tooler bidz equipment: %w", err)
		}

		toolerBidzEquipment = append(toolerBidzEquipment, toolerBidzEquipmentInventory)
	}

	return toolerBidzEquipment, nil
}

// GetToolerBidzEquipmentByID returns equipment by equipment ID.
func (f *firestoredb) GetToolerBidzEquipmentByID(ctx context.Context, id string) (models.ToolerBidzEquipment, error) {
	doc, err := f.client.Collection(toolerBidzEquipmentCollectionName).Doc(id).Get(ctx)
	if err != nil {
		return models.ToolerBidzEquipment{}, fmt.Errorf("unable to get tooler bidz equipment: %w", err)
	}

	var toolerBidzEquipment models.ToolerBidzEquipment

	err = doc.DataTo(&toolerBidzEquipment)
	if err != nil {
		return models.ToolerBidzEquipment{}, fmt.Errorf("unable to get tooler bidz equipment: %w", err)
	}

	return toolerBidzEquipment, nil
}

// GetEquipmentInventoryByName returns equipment by equipment ID.
func (f *firestoredb) GetEquipmentInventoryByName(ctx context.Context, name string) (models.ToolerBidzEquipment, error) {
	iter := f.client.Collection(toolerBidzEquipmentCollectionName).Where("name_en", "==", name).Documents(ctx)

	var toolerBidzEquipment models.ToolerBidzEquipment

	for {
		doc, err := iter.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return models.ToolerBidzEquipment{}, fmt.Errorf("unable to get tooler bidz equipment: %w", err)
		}

		err = doc.DataTo(&toolerBidzEquipment)
		if err != nil {
			return models.ToolerBidzEquipment{}, fmt.Errorf("unable to get tooler bidz equipment: %w", err)
		}
	}

	return toolerBidzEquipment, nil
}

// GetEquipmentInventoryByNameFR returns equipment by French name.
func (f *firestoredb) GetEquipmentInventoryByNameFR(ctx context.Context, name string) (models.ToolerBidzEquipment, error) {
	iter := f.client.Collection(toolerBidzEquipmentCollectionName).Where("name_fr", "==", name).Documents(ctx)

	var toolerBidzEquipment models.ToolerBidzEquipment

	for {
		doc, err := iter.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return models.ToolerBidzEquipment{}, fmt.Errorf("unable to get tooler bidz equipment by French name: %w", err)
		}

		err = doc.DataTo(&toolerBidzEquipment)
		if err != nil {
			return models.ToolerBidzEquipment{}, fmt.Errorf("unable to parse tooler bidz equipment by French name: %w", err)
		}
	}

	return toolerBidzEquipment, nil
}

// DropToolerBidzEquipments drop all tooler bidz equipment.
func (f *firestoredb) DropToolerBidzEquipments(ctx context.Context) error {
	iter := f.client.Collection(toolerBidzEquipmentCollectionName).Documents(ctx)

	for {
		doc, err := iter.Next()
		if errors.Is(err, iterator.Done) {
			break
		}

		if err != nil {
			return fmt.Errorf("unable to iterate over tooler bidz equipment: %w", err)
		}

		_, err = doc.Ref.Delete(ctx)
		if err != nil {
			return fmt.Errorf("unable to delete tooler bidz equipment: %w", err)
		}
	}

	return nil
}
