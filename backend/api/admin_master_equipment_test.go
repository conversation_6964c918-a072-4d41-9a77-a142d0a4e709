package api

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"github.com/vima-inc/derental/api/middleware"
	"github.com/vima-inc/derental/db/mocks"
	"github.com/vima-inc/derental/models"
	"github.com/vima-inc/derental/service"
)

func TestGetMasterEquipmentByID(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Setup
	mockDB := &mocks.Database{}
	mockService := service.New(service.WithDB(mockDB))

	// Mock data
	mockEquipment := models.ToolerBidzEquipment{
		ID:     "test-id",
		NameEN: "Test Equipment",
		NameFR: "Équipement de Test",
		Brand:  "Test Brand",
		Model:  "Test Model",
	}

	// Setup mock expectations
	mockDB.On("GetToolerBidzEquipmentByID", mock.Anything, "test-id").Return(mockEquipment, nil)

	// Create request
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("GET", "/admin/master-equipment/test-id", nil)
	c.Params = gin.Params{{Key: "id", Value: "test-id"}}

	// Set admin email in context
	ctx := context.WithValue(c.Request.Context(), middleware.EmailKey, "<EMAIL>")
	c.Request = c.Request.WithContext(ctx)

	// Execute
	handler := getMasterEquipmentByID(mockService)
	handler(c)

	// Assert
	assert.Equal(t, http.StatusOK, w.Code)

	var response models.ToolerBidzEquipment
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, mockEquipment.ID, response.ID)
	assert.Equal(t, mockEquipment.NameEN, response.NameEN)

	mockDB.AssertExpectations(t)
}

func TestGetMasterEquipmentByID_Unauthorized(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Setup
	mockDB := &mocks.Database{}
	mockService := service.New(service.WithDB(mockDB))

	// Create request without admin email in context
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("GET", "/admin/master-equipment/test-id", nil)
	c.Params = gin.Params{{Key: "id", Value: "test-id"}}

	// Execute
	handler := getMasterEquipmentByID(mockService)
	handler(c)

	// Assert
	assert.Equal(t, http.StatusUnauthorized, w.Code)
}

func TestUpdateMasterEquipment(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Setup
	mockDB := &mocks.Database{}
	mockService := service.New(service.WithDB(mockDB))

	// Mock data
	updateData := models.ToolerBidzEquipment{
		ID:     "test-id",
		NameEN: "Updated Equipment",
		NameFR: "Équipement Mis à Jour",
		Brand:  "Updated Brand",
		Model:  "Updated Model",
	}

	existingEquipment := models.ToolerBidzEquipment{
		ID:     "test-id",
		NameEN: "Original Equipment",
		NameFR: "Équipement Original",
	}

	// Setup mock expectations
	mockDB.On("GetToolerBidzEquipmentByID", mock.Anything, "test-id").Return(existingEquipment, nil)

	// Mock validation calls - return empty list (no similar names found)
	mockDB.On("GetAllToolerBidzEquipment", mock.Anything).Return([]models.ToolerBidzEquipment{}, nil)

	mockDB.On("UpdateToolerBidzEquipment", mock.Anything, mock.MatchedBy(func(eq models.ToolerBidzEquipment) bool {
		return eq.ID == "test-id" && eq.NameEN == "Updated Equipment"
	})).Return(nil)

	// Create request body
	jsonData, _ := json.Marshal(updateData)

	// Create request
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("PUT", "/admin/master-equipment/test-id", bytes.NewBuffer(jsonData))
	c.Request.Header.Set("Content-Type", "application/json")
	c.Params = gin.Params{{Key: "id", Value: "test-id"}}

	// Set admin email in context
	ctx := context.WithValue(c.Request.Context(), middleware.EmailKey, "<EMAIL>")
	c.Request = c.Request.WithContext(ctx)

	// Execute
	handler := updateMasterEquipment(mockService)
	handler(c)

	// Assert
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]string
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Master equipment updated successfully", response["message"])

	mockDB.AssertExpectations(t)
}

func TestUpdateMasterEquipment_ValidationError(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Setup
	mockDB := &mocks.Database{}
	mockService := service.New(service.WithDB(mockDB))

	// Invalid data (missing required fields)
	updateData := models.ToolerBidzEquipment{
		ID:     "test-id",
		NameEN: "", // Empty required field
		NameFR: "", // Empty required field
	}

	// Create request body
	jsonData, _ := json.Marshal(updateData)

	// Create request
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("PUT", "/admin/master-equipment/test-id", bytes.NewBuffer(jsonData))
	c.Request.Header.Set("Content-Type", "application/json")
	c.Params = gin.Params{{Key: "id", Value: "test-id"}}

	// Set admin email in context
	ctx := context.WithValue(c.Request.Context(), middleware.EmailKey, "<EMAIL>")
	c.Request = c.Request.WithContext(ctx)

	// Mock the GetToolerBidzEquipmentByID call (needed for name comparison)
	existingEquipment := models.ToolerBidzEquipment{
		ID:     "test-id",
		NameEN: "Existing Name",
		NameFR: "Nom Existant",
	}
	mockDB.On("GetToolerBidzEquipmentByID", mock.Anything, "test-id").Return(existingEquipment, nil)

	// Execute
	handler := updateMasterEquipment(mockService)
	handler(c)

	// Assert
	assert.Equal(t, http.StatusBadRequest, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Validation failed", response["error"])
	assert.Contains(t, response, "errors")
}

// TestUploadMasterEquipmentImage is commented out because it requires complex storage mocking
// The functionality is tested through integration tests
// func TestUploadMasterEquipmentImage(t *testing.T) {
// 	// This test would require mocking storage operations which is complex
// 	// The upload functionality is covered by integration tests
// }

func TestValidateMasterEquipment(t *testing.T) {
	// Setup mock service
	mockDB := &mocks.Database{}
	mockService := service.New(service.WithDB(mockDB))
	ctx := context.Background()

	tests := []struct {
		name      string
		equipment models.ToolerBidzEquipment
		setupMock func()
		wantErrs  int
	}{
		{
			name: "Valid equipment",
			equipment: models.ToolerBidzEquipment{
				NameEN:    "Valid Name EN",
				NameFR:    "Valid Name FR",
				ImageLink: "https://example.com/image.jpg",
			},
			setupMock: func() {
				// Mock that no similar equipment exists
				mockDB.On("GetAllToolerBidzEquipment", ctx).Return([]models.ToolerBidzEquipment{}, nil)
			},
			wantErrs: 0,
		},
		{
			name: "Missing required fields",
			equipment: models.ToolerBidzEquipment{
				NameEN: "",
				NameFR: "",
			},
			setupMock: func() {
				// No mock needed for empty names
			},
			wantErrs: 1, // Only English name is required, French name is optional
		},
		{
			name: "Invalid image URL",
			equipment: models.ToolerBidzEquipment{
				NameEN:    "Valid Name EN",
				NameFR:    "Valid Name FR",
				ImageLink: "invalid-url",
			},
			setupMock: func() {
				// Mock that no similar equipment exists
				mockDB.On("GetAllToolerBidzEquipment", ctx).Return([]models.ToolerBidzEquipment{}, nil)
			},
			wantErrs: 1,
		},
		{
			name: "Negative ARA IDs",
			equipment: models.ToolerBidzEquipment{
				NameEN:      "Valid Name EN",
				NameFR:      "Valid Name FR",
				ARALevel1ID: -1,
				ARALevel2ID: -1,
			},
			setupMock: func() {
				// Mock that no similar equipment exists
				mockDB.On("GetAllToolerBidzEquipment", ctx).Return([]models.ToolerBidzEquipment{}, nil)
			},
			wantErrs: 2,
		},
		{
			name: "Similar English name",
			equipment: models.ToolerBidzEquipment{
				NameEN: "Existing Name EN",
				NameFR: "Valid Name FR",
			},
			setupMock: func() {
				// Mock that equipment exists with similar English name
				existingEquipment := models.ToolerBidzEquipment{ID: "existing-id", NameEN: "existing name en", NameFR: "Different Name FR"}
				mockDB.On("GetAllToolerBidzEquipment", ctx).Return([]models.ToolerBidzEquipment{existingEquipment}, nil)
			},
			wantErrs: 1,
		},
		{
			name: "Similar French name",
			equipment: models.ToolerBidzEquipment{
				NameEN: "Valid Name EN",
				NameFR: "Existing Name FR",
			},
			setupMock: func() {
				// Mock that equipment exists with similar French name
				existingEquipment := models.ToolerBidzEquipment{ID: "existing-id", NameEN: "Different Name EN", NameFR: "existing name fr"}
				mockDB.On("GetAllToolerBidzEquipment", ctx).Return([]models.ToolerBidzEquipment{existingEquipment}, nil)
			},
			wantErrs: 1,
		},
		{
			name: "Very similar name with typo - should be detected",
			equipment: models.ToolerBidzEquipment{
				NameEN: "Excavator",
				NameFR: "Valid Name FR",
			},
			setupMock: func() {
				// Mock that equipment exists with very similar name (typo - 95%+ similarity)
				existingEquipment := models.ToolerBidzEquipment{ID: "existing-id", NameEN: "Excavater", NameFR: "Different Name FR"}
				mockDB.On("GetAllToolerBidzEquipment", ctx).Return([]models.ToolerBidzEquipment{existingEquipment}, nil)
			},
			wantErrs: 1,
		},
		{
			name: "Different enough names should pass",
			equipment: models.ToolerBidzEquipment{
				NameEN: "Excavator",
				NameFR: "Valid Name FR",
			},
			setupMock: func() {
				// Mock that equipment exists with different enough name
				existingEquipment := models.ToolerBidzEquipment{ID: "existing-id", NameEN: "Bulldozer", NameFR: "Different Name FR"}
				mockDB.On("GetAllToolerBidzEquipment", ctx).Return([]models.ToolerBidzEquipment{existingEquipment}, nil)
			},
			wantErrs: 0,
		},
		{
			name: "Self Propelled Dowell Drill should pass with different equipment",
			equipment: models.ToolerBidzEquipment{
				NameEN: "Self Propelled Dowell Drill",
				NameFR: "Perceuse Dowell Autopropulsée",
			},
			setupMock: func() {
				// Mock some common equipment that should not conflict
				existingEquipment1 := models.ToolerBidzEquipment{ID: "existing-1", NameEN: "Excavator", NameFR: "Excavatrice"}
				existingEquipment2 := models.ToolerBidzEquipment{ID: "existing-2", NameEN: "Bulldozer", NameFR: "Bouteur"}
				existingEquipment3 := models.ToolerBidzEquipment{ID: "existing-3", NameEN: "Air Compressor", NameFR: "Compresseur d'air"}
				existingEquipment4 := models.ToolerBidzEquipment{ID: "existing-4", NameEN: "Concrete Mixer", NameFR: "Malaxeur à béton"}
				existingEquipment5 := models.ToolerBidzEquipment{ID: "existing-5", NameEN: "Drill", NameFR: "Perceuse"}
				mockDB.On("GetAllToolerBidzEquipment", ctx).Return([]models.ToolerBidzEquipment{
					existingEquipment1, existingEquipment2, existingEquipment3, existingEquipment4, existingEquipment5,
				}, nil)
			},
			wantErrs: 0,
		},
		{
			name: "Self Propelled Dowell Drill exact duplicate should be detected",
			equipment: models.ToolerBidzEquipment{
				NameEN: "Self Propelled Dowell Drill",
				NameFR: "Perceuse Dowell Autopropulsée",
			},
			setupMock: func() {
				// Mock that exact same equipment already exists
				existingEquipment := models.ToolerBidzEquipment{ID: "existing-id", NameEN: "Self Propelled Dowell Drill", NameFR: "Perceuse Dowell Autopropulsée"}
				mockDB.On("GetAllToolerBidzEquipment", ctx).Return([]models.ToolerBidzEquipment{existingEquipment}, nil)
			},
			wantErrs: 2, // Should detect both English and French duplicates
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Reset mock expectations
			mockDB.ExpectedCalls = nil
			mockDB.Calls = nil

			// Setup mock expectations
			tt.setupMock()

			errors := validateMasterEquipment(ctx, mockService, tt.equipment, "")
			assert.Equal(t, tt.wantErrs, len(errors))

			// Verify all expectations were met
			mockDB.AssertExpectations(t)
		})
	}
}

func TestCheckMasterEquipmentNames(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name           string
		requestBody    map[string]interface{}
		setupMock      func(*mocks.Database)
		expectedResult map[string]interface{}
	}{
		{
			name: "Self Propelled Dowell Drill - should detect duplicate",
			requestBody: map[string]interface{}{
				"name_en": "Self Propelled Dowell Drill",
				"name_fr": "Perceuse Dowell Autopropulsée",
			},
			setupMock: func(mockDB *mocks.Database) {
				// Mock that exact same equipment already exists
				existingEquipment := models.ToolerBidzEquipment{
					ID:     "existing-id",
					NameEN: "Self Propelled Dowell Drill",
					NameFR: "Perceuse Dowell Autopropulsée",
				}
				mockDB.On("GetAllToolerBidzEquipment", mock.Anything).Return([]models.ToolerBidzEquipment{existingEquipment}, nil).Twice()
			},
			expectedResult: map[string]interface{}{
				"name_en_available": false,
				"name_fr_available": false,
			},
		},
		{
			name: "Self Propelled Dowell Drill - should pass with different equipment",
			requestBody: map[string]interface{}{
				"name_en": "Self Propelled Dowell Drill",
				"name_fr": "Perceuse Dowell Autopropulsée",
			},
			setupMock: func(mockDB *mocks.Database) {
				// Mock different equipment that should not conflict
				existingEquipment := models.ToolerBidzEquipment{
					ID:     "existing-id",
					NameEN: "Excavator",
					NameFR: "Excavatrice",
				}
				mockDB.On("GetAllToolerBidzEquipment", mock.Anything).Return([]models.ToolerBidzEquipment{existingEquipment}, nil).Twice()
			},
			expectedResult: map[string]interface{}{
				"name_en_available": true,
				"name_fr_available": true,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			mockDB := &mocks.Database{}
			mockService := service.New(service.WithDB(mockDB))

			// Setup mock
			tt.setupMock(mockDB)

			// Create request
			body, _ := json.Marshal(tt.requestBody)
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Request = httptest.NewRequest("POST", "/admin/master-equipment/check-names", bytes.NewBuffer(body))
			c.Request.Header.Set("Content-Type", "application/json")

			// Set admin email in context
			ctx := context.WithValue(c.Request.Context(), middleware.EmailKey, "<EMAIL>")
			c.Request = c.Request.WithContext(ctx)

			// Execute
			checkEquipmentNameAvailability(mockService)(c)

			// Assert
			assert.Equal(t, http.StatusOK, w.Code)

			var response map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &response)
			assert.NoError(t, err)

			assert.Equal(t, tt.expectedResult["name_en_available"], response["name_en_available"])
			assert.Equal(t, tt.expectedResult["name_fr_available"], response["name_fr_available"])

			mockDB.AssertExpectations(t)
		})
	}
}
