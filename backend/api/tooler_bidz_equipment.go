package api

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/vima-inc/derental/api/middleware"
	"github.com/vima-inc/derental/models"
	"github.com/vima-inc/derental/service"
)

func addToolerBidzEquipment(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		_, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		var toolerBidzEquipmentInventory models.ToolerBidzEquipment

		err := c.BindJSON(&toolerBidzEquipmentInventory)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		err = svc.AddToolerBidzEquipment(c.Request.Context(), &toolerBidzEquipmentInventory)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.Status(http.StatusOK)
	}
}

func updateToolerBidzEquipment(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		_, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		var toolerBidzEquipmentInventory models.ToolerBidzEquipment

		err := c.BindJSON(&toolerBidzEquipmentInventory)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		err = svc.UpdateToolerBidzEquipment(c.Request.Context(), toolerBidzEquipmentInventory)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.Status(http.StatusOK)
	}
}

func deleteToolerBidzEquipment(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		_, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		id := c.Param("tooler_bidz_equipment_id")

		err := svc.DeleteToolerBidzEquipment(c.Request.Context(), id)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.Status(http.StatusOK)
	}
}

func getAllToolerBidzEquipment(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		_, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		toolerBidzEquipment, err := svc.GetAllToolerBidzEquipment(c.Request.Context())
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.JSON(http.StatusOK, toolerBidzEquipment)
	}
}

func uploadEquipmentPhoto(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		_, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		id := c.Param("tooler_bidz_equipment_id")

		file, header, err := c.Request.FormFile("file")
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}
		defer file.Close()

		err = svc.UploadPhotoToEquipmentLibrary(c.Request.Context(), id, header.Filename, file)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.Status(http.StatusOK)
	}
}

func getBidzEquipmentByID(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		id := c.Param("tooler_bidz_equipment_id")

		toolerBidzEquipment, err := svc.GetBidzEquipmentByID(c.Request.Context(), id)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.JSON(http.StatusOK, toolerBidzEquipment)
	}
}

func dropToolerBidzEquipments(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.Request.Context().Value(middleware.UIDKey)

		_, ok := uid.(string)
		if !ok {
			c.Status(http.StatusBadRequest)

			return
		}

		err := svc.DropToolerBidzEquipments(c.Request.Context())
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)

			return
		}

		c.Status(http.StatusOK)
	}
}
