package api

import (
	"github.com/gin-gonic/gin"
	"github.com/vima-inc/derental/api/middleware"
)

// Options is the options for the server.
type Options func(*Server)

// WithCors sets the cors middleware.
func WithCors() Options {
	return func(s *Server) {
		s.handler.Use(middleware.Cors())
	}
}

// WithAdminRoutes sets the admin routes.
func WithAdminRoutes(auth gin.HandlerFunc) Options {
	return func(s *Server) {
		admin := s.handler.Group("/admin").Use(middleware.Latency())
		{
			// Public admin routes (for login)
			admin.POST("/signin", adminSignin(s.service))
		}

		// Protected admin routes
		adminAuth := s.handler.Group("/admin").Use(middleware.Latency()).Use(auth)
		{
			adminAuth.GET("/master-inventory", getMasterInventory(s.service))
			adminAuth.GET("/master-inventory/export", exportMasterInventoryCSV(s.service))
			adminAuth.POST("/master-equipment", createMasterEquipment(s.service))
			adminAuth.POST("/master-equipment/check-names", checkEquipmentNameAvailability(s.service))
			adminAuth.GET("/master-equipment/:id", getMasterEquipmentByID(s.service))
			adminAuth.PUT("/master-equipment/:id", updateMasterEquipment(s.service))
			adminAuth.POST("/master-equipment/:id/image", uploadMasterEquipmentImage(s.service))
			adminAuth.POST("/generate-description", generateEquipmentDescription(s.service))
			adminAuth.POST("/generate-french-name", generateFrenchName(s.service))
			adminAuth.POST("/master-equipment/:id/classify", classifyMasterEquipment(s.service))
			adminAuth.GET("/dashboard/stats", getAdminDashboardStats(s.service))
			adminAuth.GET("/ara-level1-categories", getARALevel1Categories(s.service))
			adminAuth.GET("/ara-level2-types", getARALevel2Types(s.service))
		}
	}
}
