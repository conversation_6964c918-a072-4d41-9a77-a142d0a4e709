package main

import (
	"context"
	"errors"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"contrib.go.opencensus.io/exporter/stackdriver"
	firebase "firebase.google.com/go"
	"google.golang.org/api/option"

	"github.com/dgraph-io/ristretto"
	"github.com/vima-inc/derental/api"
	"github.com/vima-inc/derental/api/middleware"
	firebaseauth "github.com/vima-inc/derental/auth/firebase"
	"github.com/vima-inc/derental/broker/pubsub"
	"github.com/vima-inc/derental/config"
	firestoredb "github.com/vima-inc/derental/db/firestore"
	"github.com/vima-inc/derental/external/openrouter"
	"github.com/vima-inc/derental/mailer"
	"github.com/vima-inc/derental/mailer/ses"
	"github.com/vima-inc/derental/models"
	stripeclient "github.com/vima-inc/derental/payment/stripe"
	"github.com/vima-inc/derental/pdf/templatr"
	"github.com/vima-inc/derental/service"
	"github.com/vima-inc/derental/storage"
	googlestorage "github.com/vima-inc/derental/storage/google-storage"
)

const (
	SendgridForgotPasswordTemplateID              = "d-a3852d9dbafa4a72bf76b9a7d2466d09"
	SendgridForgotPasswordTemplateIDFR            = "d-843978a9798a4f6eb043d695257e54f6"
	SendgridBookingDeclineTemplateID              = "d-084952d45f904e7b87b63a2c37793581"
	SendgridBookingAcceptTemplateID               = "d-efcc9726b50f4fd8acf5009fe79466a3"
	SendgridBookingLodgerRequestID                = "d-509b882a1a374536aa69c13f588f50a6"
	SendgridBookingEquipperRequestID              = "d-edc7429243e843c6a8add61e0539b899"
	SendgridSendInvitationMemberLodgerID          = "d-f434e716ebc64d449c4b68f8c58b0059"
	SendgridSendInvitationMemberLodgerIDFR        = "d-eff9a21489364445867203d35167b0f3"
	SendgridSendRenterBidzRequestID               = "d-294ad47533d64365be73bd23148c4e7d"
	SendgridSendEquipperBidzRequestID             = "d-a02eeebc4d6545178511602fffcf2f90"
	SendgridSendRenterBidzOfferID                 = "d-83e51f9c48eb4c5a85acd2c94779477c"
	SendgridSendConfirmationRenterBidzOfferID     = "d-d74baf6a05b1457db3f33a3db1548e10"
	SendgridSendConfirmationEquipperBidzOfferID   = "d-0020ba781d394997bb5512583b0f729a"
	SendgridSendRenterBidzRequestIDFR             = "d-391691ee0ce64b909db7bd3b1bd9c820"
	SendgridSendEquipperBidzRequestIDFR           = "d-28a4e603d95c47ae9cf071b2241ef094"
	SendgridSendRenterBidzOfferIDFR               = "d-c0943f4784144ef0a55f2a2810c8ab7a"
	SendgridSendConfirmationRenterBidzOfferIDFR   = "d-c23bcf02ef514ca59fd1701baade2004"
	SendgridSendConfirmationEquipperBidzOfferIDFR = "d-0a17838ef1814824b0f8aa553167fb8a"
	SendgridSendCanceledEquipperBidzOfferID       = "d-5e1e8114c42744d18c4b9027c77d45f1"
	SendgridSendCanceledRenterBidzOfferID         = "d-2008793794be4578b2b7986c02814c6c"
	SendgridSendCanceledEquipperBidzOfferIDFR     = "d-6e8b0cd4a7544269abe405c7b7d455e0"
	SendgridSendCanceledRenterBidzOfferIDFR       = "d-e9a0298675304f1589ff15a02a14f710"
	SendgridSendWelcomeTemplateIDFR               = "d-761fc65c6de14ce0836876dc4eefb840"
	SendgridSendWelcomeTemplateID                 = "d-66d978f3bc714c84a35e9e84b7a21674"
	SendgridLeadTemplateID                        = "d-034b7f37166849539b4ecab7e2502449"
	SendgridToolerTeamEmail                       = "<EMAIL>"
	SendgridCancelBookingTemplateID               = "d-49d445c1662248c78fe90a297f5986fb"
	SendgridCancelBookingTemplateIDFR             = "d-07434831fb1641f99da89f5af26661d6"
	SendgridBookingEquipperRequestIDFR            = "d-aa4274315d7c4f62a5250f7342722fe7"
	SendgridBookingLodgerRequestIDFR              = "d-e81c8570a4b64f6b978b9ab52641b8d5"
	SendgridBookingDeclineTemplateIDFR            = "d-89c31dfb4dc243a3915300019ce5652e"
	SendgridBookingAcceptTemplateIDFR             = "d-e5c6bb0cc32e4bfc96f53d8c7852b952"
	SendgridSendEquipperWelcomeTemplateID         = "d-112dcebfcabc40bb9fd30ef6a58b0dd1"
	SendgridSendEquipperWelcomeTemplateIDFR       = "d-3509035ce6b941d686483fb23339c121"
	SendEquipmentChangeTemplateID                 = "a4fb871d-9fc6-40b0-baf5-5c52710803a0"
)

func main() {
	c, err := config.New()
	if err != nil {
		log.Fatalf("unable to init config %+v", err)
	}

	if c.Mode == "production" {
		sd, err := initStackdriver(c.ProjectID)
		if err != nil {
			log.Panicf("unable to init stackdriver %+v", err)
		}

		defer sd.Flush()

		err = sd.StartMetricsExporter()
		if err != nil {
			log.Panicf("unable to start stackdriver exporter %+v", err)
		}

		defer sd.StopMetricsExporter()
	}

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	app := initFirebase(ctx, c)

	auth, err := firebaseauth.New(c.FirebaseAPIKey, app)
	if err != nil {
		log.Panicf("unable to initialize firebase auth: %v", err)
	}

	firestore, err := firestoredb.New(ctx, app)
	if err != nil {
		log.Panicf("unable to initialize firestore: %v", err)
	}

	storage, err := googlestorage.New(ctx, c.FirebaseStorageBucket)
	if err != nil {
		log.Panicf("unable to initialize google storage: %v", err)
	}

	broker, err := pubsub.New(ctx, c.ProjectID)
	if err != nil {
		log.Panicf("unable to initialize pubsub: %v", err)
	}

	mailer := initMail(c, storage)

	stripeClient := stripeclient.New(c.StripeAPIKey, c.StripeWebhookSecret, c.FrontendURL,
		map[models.Currency]map[models.Address]string{
			models.USD: {
				models.Address{State: "Arizona", CountryCode: "US"}:    c.ArizonaTaxeRateID,
				models.Address{State: "California", CountryCode: "US"}: c.CaliforniaTaxRateID,
				models.Address{State: "Nevada", CountryCode: "US"}:     c.NevadaTaxRateID,
				models.Address{State: "Texas", CountryCode: "US"}:      c.TexasTaxRateID,
			},
			models.SAR: {
				models.Address{CountryCode: "SA"}: c.SARTaxeRateID,
			},
		})

	cache, err := ristretto.NewCache(&ristretto.Config{
		NumCounters: 1e7,
		MaxCost:     128 << 20,
		BufferItems: 64,
	})
	if err != nil {
		log.Panicf("unable to initialize cache: %v", err)
	}

	pdf := templatr.New(c.TemplatrAPIKey)

	// Initialize OpenRouter client for AI description generation
	var openRouterClient *openrouter.Client
	if c.OpenRouterAPIKey != "" {
		openRouterClient = openrouter.NewClient(c.OpenRouterAPIKey)
		log.Printf("OpenRouter client initialized for AI description generation (API key: %s...%s)",
			c.OpenRouterAPIKey[:8], c.OpenRouterAPIKey[len(c.OpenRouterAPIKey)-4:])
	} else {
		log.Println("Warning: OpenRouter API key not provided, AI description generation will not be available")
		log.Printf("Debug: OPENROUTER_API_KEY environment variable value: '%s'", c.OpenRouterAPIKey)
	}

	svc := service.New(
		service.WithDB(firestore),
		service.WithAuth(auth),
		service.WithMailer(mailer),
		service.WithStorage(storage),
		service.WithFrontendURL(c.FrontendURL),
		service.WithAirtableAPIKey(c.AirtableToken),
		service.WithEquipmentLibraryURL(c.EquipmentLibraryURL),
		service.WithPayment(stripeClient),
		service.WithBookingNotificationEmail(c.BookingNotificationEmail),
		service.WithSendGridSender(c.SendgridSender),
		service.WithProjectID(c.ProjectID),
		service.WithBroker(broker),
		service.WithCache(cache),
		service.WithPDFGenerator(pdf),
		service.WithOpenRouterClient(openRouterClient),
		service.WithTranslator(c.GoogleTranslationApiKey),
	)

	opts := []api.Options{
		api.WithCors(),
		api.WithDefaultRoutes(middleware.Authorization(auth)),
		api.WithAdminRoutes(middleware.AdminAuthorization(auth)),
	}
	srv := api.New(c.Port, svc, opts...)

	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM, os.Interrupt)

	go func() {
		err = srv.StartHTTP()
		if err != nil && errors.Is(err, http.ErrServerClosed) {
			log.Printf("An error was occurred %v", err)
			<-quit
		}
	}()

	<-quit

	log.Print("Shutdown Server ...")

	if err := srv.Shutdown(ctx); err != nil {
		log.Printf("Server Shutdown: %v", err)
	}

	log.Printf("Server exiting")
}

func initMail(c *config.Config, storage storage.Storage) mailer.Mailer {
	bucket := fmt.Sprintf("%s_private_informations", c.ProjectID)
	ses, err := ses.New(c.AWSAccessKey, c.AWSSecretKey, c.AWSRegion, c.SendgridSender, storage, bucket)
	if err != nil {
		log.Panicf("unable to initialize ses: %v", err)
	}

	ses.SetTemplateIDs(map[string]string{
		mailer.ForgotPasswordTemplateID:                      SendgridForgotPasswordTemplateID,
		mailer.ForgotPasswordTemplateIDFR:                    SendgridForgotPasswordTemplateIDFR,
		mailer.BookingDeclineTemplateID:                      SendgridBookingDeclineTemplateID,
		mailer.BookingAcceptTemplateID:                       SendgridBookingAcceptTemplateID,
		mailer.SendgridBookingLodgerRequestID:                SendgridBookingLodgerRequestID,
		mailer.SendgridBookingEquipperRequestID:              SendgridBookingEquipperRequestID,
		mailer.SendInvitationMemberLodgerID:                  SendgridSendInvitationMemberLodgerID,
		mailer.SendInvitationMemberLodgerIDFR:                SendgridSendInvitationMemberLodgerIDFR,
		mailer.SendRenterBidzRequestID:                       SendgridSendRenterBidzRequestID,
		mailer.SendEquipperBidzRequestID:                     SendgridSendEquipperBidzRequestID,
		mailer.SendgridSendRenterBidzOfferID:                 SendgridSendRenterBidzOfferID,
		mailer.SendgridSendConfirmationRenterBidzOfferID:     SendgridSendConfirmationRenterBidzOfferID,
		mailer.SendgridSendConfirmationEquipperBidzOfferID:   SendgridSendConfirmationEquipperBidzOfferID,
		mailer.SendRenterBidzRequestIDFR:                     SendgridSendRenterBidzRequestIDFR,
		mailer.SendEquipperBidzRequestIDFR:                   SendgridSendEquipperBidzRequestIDFR,
		mailer.SendgridSendRenterBidzOfferIDFR:               SendgridSendRenterBidzOfferIDFR,
		mailer.SendgridSendConfirmationRenterBidzOfferIDFR:   SendgridSendConfirmationRenterBidzOfferIDFR,
		mailer.SendgridSendConfirmationEquipperBidzOfferIDFR: SendgridSendConfirmationEquipperBidzOfferIDFR,
		mailer.SendgridSendCanceledEquipperBidzOfferID:       SendgridSendCanceledEquipperBidzOfferID,
		mailer.SendgridSendCanceledRenterBidzOfferID:         SendgridSendCanceledRenterBidzOfferID,
		mailer.SendgridSendCanceledEquipperBidzOfferIDFR:     SendgridSendCanceledEquipperBidzOfferIDFR,
		mailer.SendgridSendCanceledRenterBidzOfferIDFR:       SendgridSendCanceledRenterBidzOfferIDFR,
		mailer.SendWelcomeTemplateIDFR:                       SendgridSendWelcomeTemplateIDFR,
		mailer.SendWelcomeTemplateID:                         SendgridSendWelcomeTemplateID,
		mailer.SendgridLeadTemplateID:                        SendgridLeadTemplateID,
		mailer.SendgridToolerTeamEmail:                       SendgridToolerTeamEmail,
		mailer.SendgridCancelBookingTemplateID:               SendgridCancelBookingTemplateID,
		mailer.SendgridCancelBookingTemplateIDFR:             SendgridCancelBookingTemplateIDFR,
		mailer.SendgridBookingEquipperRequestIDFR:            SendgridBookingEquipperRequestIDFR,
		mailer.SendgridBookingLodgerRequestIDFR:              SendgridBookingLodgerRequestIDFR,
		mailer.SendgridBookingDeclineTemplateIDFR:            SendgridBookingDeclineTemplateIDFR,
		mailer.SendgridBookingAcceptTemplateIDFR:             SendgridBookingAcceptTemplateIDFR,
		mailer.SendgridSendEquipperWelcomeTemplateID:         SendgridSendEquipperWelcomeTemplateID,
		mailer.SendgridSendEquipperWelcomeTemplateIDFR:       SendgridSendEquipperWelcomeTemplateIDFR,
		mailer.SendEquipmentChangeTemplateID:                 SendEquipmentChangeTemplateID,
	})

	return ses
}

func initFirebase(ctx context.Context, c *config.Config) *firebase.App {
	isDevelopment := c.Mode != "production"

	options := []option.ClientOption{}
	if isDevelopment {
		options = append(options, option.WithCredentialsFile("./key.json"))
	}

	config := &firebase.Config{
		StorageBucket: c.FirebaseStorageBucket,
	}

	app, err := firebase.NewApp(ctx, config, options...)
	if err != nil {
		log.Fatalf("unable to initialize firebase app: %v", err)
	}

	return app
}

func initStackdriver(projectID string) (*stackdriver.Exporter, error) {
	sd, err := stackdriver.NewExporter(stackdriver.Options{
		ProjectID:         projectID,
		ReportingInterval: 60 * time.Second,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create stackdriver exporter: %w", err)
	}

	return sd, err
}
