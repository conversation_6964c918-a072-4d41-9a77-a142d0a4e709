package config

import (
	"fmt"
	"log"

	"github.com/joho/godotenv"
	"github.com/kelseyhightower/envconfig"
)

// Config is the configuration of the application.
type Config struct {
	ProjectID                     string `envconfig:"GOOGLE_CLOUD_PROJECT"`
	Mode                          string `envconfig:"MODE"`
	Port                          string `envconfig:"PORT"`
	FirebaseAPIKey                string `envconfig:"FIREBASE_API_KEY"`
	FirebaseStorageBucket         string `envconfig:"FIREBASE_STORAGE_BUCKET"`
	EquipmentLibraryURL           string `envconfig:"EQUIPMENT_LIBRARY_URL"`
	FrontendURL                   string `envconfig:"FRONTEND_URL"`
	AirTableURL                   string `envconfig:"AIRTABLE_API_URL"`
	AirtableToken                 string `envconfig:"AIRTABLE_API_KEY"`
	StripeAPIKey                  string `envconfig:"STRIPE_API_KEY"`
	StripeWebhookSecret           string `envconfig:"STRIPE_WEBHOOK_SECRET"`
	SlackWebhookURL               string `envconfig:"SLACK_WEBHOOK_URL"`
	SendgridAPIKey                string `envconfig:"SENDGRID_API_KEY"`
	SendgridSender                string `envconfig:"SENDGRID_SENDER"`
	AWSAccessKey                  string `envconfig:"AWS_ACCESS_KEY"`
	AWSSecretKey                  string `envconfig:"AWS_SECRET_KEY"`
	AWSRegion                     string `envconfig:"AWS_REGION"`
	BookingNotificationEmail      string `envconfig:"BOOKING_NOTIFICATION_EMAIL"`
	GoogleTranslationApiKey       string `envconfig:"GOOGLE_TRANSLATION_API_KEY"`
	TemplatrAPIKey                string `envconfig:"TEMPLATR_API_KEY"`
	ArizonaTaxeRateID             string `envconfig:"ARIZONA_TAX_RATE_ID"`
	CaliforniaTaxRateID           string `envconfig:"CALIFORNIA_TAX_RATE_ID"`
	NevadaTaxRateID               string `envconfig:"NEVADA_TAX_RATE_ID"`
	TexasTaxRateID                string `envconfig:"TEXAS_TAX_RATE_ID"`
	SARTaxeRateID                 string `envconfig:"SAR_TAX_RATE_ID"`
	EquipmentUploadTimeoutMinutes int    `envconfig:"EQUIPMENT_UPLOAD_TIMEOUT_MINUTES"`
	SkipImageUpload               bool   `envconfig:"SKIP_IMAGE_UPLOAD"`
	// OpenRouter Configuration
	OpenRouterAPIKey             string  `envconfig:"OPENROUTER_API_KEY"`
	ARAClassificationBatchSize   int     `envconfig:"ARA_CLASSIFICATION_BATCH_SIZE"`
	ARAClassificationModel       string  `envconfig:"ARA_CLASSIFICATION_MODEL"`
	ARAClassificationTemperature float64 `envconfig:"ARA_CLASSIFICATION_TEMPERATURE"`
}

// New retrun new instance of Config.
func New() (*Config, error) {
	c := Config{
		Port:                          "8080",
		Mode:                          "dev",
		EquipmentUploadTimeoutMinutes: 90,    // Default to 90 minutes for large files
		SkipImageUpload:               false, // Default to process images
		// OpenRouter defaults
		ARAClassificationBatchSize:   15,                         // Default batch size
		ARAClassificationModel:       "anthropic/claude-3-haiku", // Default model
		ARAClassificationTemperature: 0.1,                        // Default temperature
	}

	err := godotenv.Load()
	if err != nil {
		log.Println("unable to load .env file")
	}

	err = envconfig.Process("", &c)
	if err != nil {
		return nil, fmt.Errorf("unable to get envconfig %w", err)
	}

	// Debug logging for OpenRouter configuration
	if c.OpenRouterAPIKey != "" {
		log.Printf("Config: OpenRouter API key loaded successfully (length: %d)", len(c.OpenRouterAPIKey))
	} else {
		log.Println("Config: OpenRouter API key is empty or not set")
	}

	return &c, nil
}
