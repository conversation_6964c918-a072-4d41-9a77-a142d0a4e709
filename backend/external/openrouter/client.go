package openrouter

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/vima-inc/derental/models"
)

const (
	OpenRouterBaseURL = "https://openrouter.ai/api/v1"
	DefaultTimeout    = 60 * time.Second
)

// Client represents an OpenRouter API client
type Client struct {
	apiKey     string
	httpClient *http.Client
	baseURL    string
}

// NewClient creates a new OpenRouter client
func NewClient(apiKey string) *Client {
	return &Client{
		apiKey: apiKey,
		httpClient: &http.Client{
			Timeout: DefaultTimeout,
		},
		baseURL: OpenRouterBaseURL,
	}
}

// SetTimeout sets the HTTP client timeout
func (c *Client) SetTimeout(timeout time.Duration) {
	c.httpClient.Timeout = timeout
}

// ClassifyEquipmentBatch classifies a batch of equipment using OpenRouter
func (c *Client) ClassifyEquipmentBatch(ctx context.Context, equipment []models.EquipmentForClassification, araTypes []models.ARALevel2Type, config models.ARAClassificationConfig) (*models.ARAClassificationResponse, error) {
	// Check if any equipment has images and if the model supports vision
	hasImages := c.hasImages(equipment)
	supportsVision := c.supportsVision(config.Model)

	var message models.OpenRouterMessage
	var useVision bool

	if hasImages && supportsVision {
		message = c.buildVisionMessage(equipment, araTypes)
		useVision = true
	} else {
		prompt := c.buildBatchClassificationPrompt(equipment, araTypes)
		message = models.OpenRouterMessage{
			Role:    "user",
			Content: prompt,
		}
		useVision = false
	}

	request := models.OpenRouterRequest{
		Model:       config.Model,
		Temperature: config.Temperature,
		MaxTokens:   config.MaxTokensPerBatch,
		Messages:    []models.OpenRouterMessage{message},
	}

	response, err := c.makeRequest(ctx, request)
	if err != nil {
		// If vision classification failed due to image issues, try text-only fallback
		if useVision && c.isImageRelatedError(err) {
			fmt.Printf("Vision classification failed due to image issues, falling back to text-only for batch\n")

			// Build text-only message
			prompt := c.buildBatchClassificationPrompt(equipment, araTypes)
			textMessage := models.OpenRouterMessage{
				Role:    "user",
				Content: prompt,
			}

			// Retry with text-only
			textRequest := models.OpenRouterRequest{
				Model:       config.Model,
				Temperature: config.Temperature,
				MaxTokens:   config.MaxTokensPerBatch,
				Messages:    []models.OpenRouterMessage{textMessage},
			}

			response, err = c.makeRequest(ctx, textRequest)
			if err != nil {
				return &models.ARAClassificationResponse{
					Success: false,
					Error:   fmt.Sprintf("OpenRouter API error (after text fallback): %v", err),
				}, err
			}
		} else {
			return &models.ARAClassificationResponse{
				Success: false,
				Error:   fmt.Sprintf("OpenRouter API error: %v", err),
			}, err
		}
	}

	if response.Error != nil {
		return &models.ARAClassificationResponse{
			Success: false,
			Error:   fmt.Sprintf("OpenRouter API error: %s", response.Error.Message),
		}, fmt.Errorf("openrouter error: %s", response.Error.Message)
	}

	if len(response.Choices) == 0 {
		return &models.ARAClassificationResponse{
			Success: false,
			Error:   "No response choices returned from OpenRouter",
		}, fmt.Errorf("no response choices")
	}

	// Parse the AI response
	content, ok := response.Choices[0].Message.Content.(string)
	if !ok {
		return &models.ARAClassificationResponse{
			Success: false,
			Error:   "Invalid response content type",
		}, fmt.Errorf("invalid response content type")
	}
	classifications, err := c.parseClassificationResponse(content, equipment)
	if err != nil {
		return &models.ARAClassificationResponse{
			Success: false,
			Error:   fmt.Sprintf("Failed to parse AI response: %v", err),
		}, err
	}

	return &models.ARAClassificationResponse{
		Classifications: classifications,
		Success:         true,
	}, nil
}

// makeRequest makes an HTTP request to OpenRouter API
func (c *Client) makeRequest(ctx context.Context, request models.OpenRouterRequest) (*models.OpenRouterResponse, error) {
	jsonData, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", c.baseURL+"/chat/completions", bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+c.apiKey)
	req.Header.Set("HTTP-Referer", "https://derental.com")
	req.Header.Set("X-Title", "Derental ARA Classification")

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	var response models.OpenRouterResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	return &response, nil
}

// buildBatchClassificationPrompt builds the prompt for batch classification
func (c *Client) buildBatchClassificationPrompt(equipment []models.EquipmentForClassification, araTypes []models.ARALevel2Type) string {
	prompt := `You are an expert equipment classifier. Classify the following equipment into ARA (Associated Rental Association) categories.

EQUIPMENT TO CLASSIFY:
`

	for i, eq := range equipment {
		prompt += fmt.Sprintf("%d. %s: %s\n", i+1, eq.Name, eq.Description)
	}

	prompt += `
ARA LEVEL2 TYPES (with their Level1 parent categories):
`

	for _, araType := range araTypes {
		prompt += fmt.Sprintf("- %s (Level1: %s, Level1_ID: %d, Level2_ID: %d)\n",
			araType.Name, araType.Level1Name, araType.Level1ID, araType.ID)
	}

	prompt += `
INSTRUCTIONS:
1. For each equipment, select the most appropriate ARA Level2 type from the list above
2. Consider the equipment's primary function and industry usage
3. If uncertain, choose the closest match based on functionality
4. IMPORTANT: Use ONLY the exact Level2_ID numbers from the list above - do not make up or modify IDs
5. The ara_level1_id will be automatically derived from the Level2 type, so you can set it to 0
6. Return ONLY a valid JSON array with the exact format below

REQUIRED JSON FORMAT:
[
  {"equipment_index": 1, "ara_level1_id": 0, "ara_level2_id": 10600, "confidence": "high|medium|low"},
  {"equipment_index": 2, "ara_level1_id": 0, "ara_level2_id": 11200, "confidence": "high|medium|low"}
]

Return only the JSON array, no additional text or explanation.`

	return prompt
}

// parseClassificationResponse parses the AI response into classification results
func (c *Client) parseClassificationResponse(content string, equipment []models.EquipmentForClassification) ([]models.ARAClassificationResult, error) {
	// Try to extract JSON from the response
	content = c.extractJSON(content)

	var rawResults []map[string]interface{}
	if err := json.Unmarshal([]byte(content), &rawResults); err != nil {
		return nil, fmt.Errorf("failed to parse JSON response: %w", err)
	}

	var results []models.ARAClassificationResult
	for _, raw := range rawResults {
		result := models.ARAClassificationResult{}

		if idx, ok := raw["equipment_index"].(float64); ok {
			result.EquipmentIndex = int(idx)
		}

		if level1ID, ok := raw["ara_level1_id"].(float64); ok {
			result.ARALevel1ID = int(level1ID)
		} else if level1IDStr, ok := raw["ara_level1_id"].(string); ok {
			if id, err := strconv.Atoi(level1IDStr); err == nil {
				result.ARALevel1ID = id
			}
		}

		if level2ID, ok := raw["ara_level2_id"].(float64); ok {
			result.ARALevel2ID = int(level2ID)
		} else if level2IDStr, ok := raw["ara_level2_id"].(string); ok {
			if id, err := strconv.Atoi(level2IDStr); err == nil {
				result.ARALevel2ID = id
			}
		}

		if confidence, ok := raw["confidence"].(string); ok {
			result.Confidence = confidence
		}

		// Map equipment ID from the index
		if result.EquipmentIndex > 0 && result.EquipmentIndex <= len(equipment) {
			result.EquipmentID = equipment[result.EquipmentIndex-1].ID
		}

		results = append(results, result)
	}

	return results, nil
}

// extractJSON attempts to extract JSON from a response that might contain additional text
func (c *Client) extractJSON(content string) string {
	// Find the first '[' and last ']' to extract JSON array
	start := -1
	end := -1

	for i, char := range content {
		if char == '[' && start == -1 {
			start = i
		}
		if char == ']' {
			end = i
		}
	}

	if start != -1 && end != -1 && end > start {
		return content[start : end+1]
	}

	return content
}

// EstimateCost estimates the cost for a classification request
func (c *Client) EstimateCost(equipment []models.EquipmentForClassification, araTypes []models.ARALevel2Type, config models.ARAClassificationConfig) float64 {
	prompt := c.buildBatchClassificationPrompt(equipment, araTypes)

	// Rough token estimation (4 characters per token)
	promptTokens := len(prompt) / 4
	completionTokens := len(equipment) * 50 // Estimate 50 tokens per equipment classification

	totalTokens := promptTokens + completionTokens

	// Cost estimation based on model (rough estimates)
	var costPerMToken float64
	switch config.Model {
	case "anthropic/claude-3-haiku":
		costPerMToken = 0.25 // $0.25 per million tokens
	case "anthropic/claude-3-sonnet":
		costPerMToken = 3.0 // $3.00 per million tokens
	case "openai/gpt-3.5-turbo":
		costPerMToken = 0.5 // $0.50 per million tokens
	case "openai/gpt-4":
		costPerMToken = 30.0 // $30.00 per million tokens
	default:
		costPerMToken = 1.0 // Default estimate
	}

	return float64(totalTokens) / 1000000.0 * costPerMToken
}

// hasImages checks if any equipment in the batch has image URLs
func (c *Client) hasImages(equipment []models.EquipmentForClassification) bool {
	for _, eq := range equipment {
		if eq.ImageURL != "" {
			return true
		}
	}
	return false
}

// supportsVision checks if the model supports vision capabilities
func (c *Client) supportsVision(model string) bool {
	visionModels := map[string]bool{
		"anthropic/claude-3-haiku":    true,
		"anthropic/claude-3-sonnet":   true,
		"anthropic/claude-3-opus":     true,
		"anthropic/claude-3.5-sonnet": true,
		"openai/gpt-4-vision-preview": true,
		"openai/gpt-4o":               true,
		"openai/gpt-4o-mini":          true,
	}
	return visionModels[model]
}

// isImageRelatedError checks if the error is related to image processing issues
func (c *Client) isImageRelatedError(err error) bool {
	if err == nil {
		return false
	}

	errorStr := strings.ToLower(err.Error())
	imageErrors := []string{
		"image url can not be accessed",
		"status code: 404",
		"invalid image data",
		"image not found",
		"image format",
		"image size",
		"url sources are not supported",
		"invalid_request_error",
	}

	for _, imageError := range imageErrors {
		if strings.Contains(errorStr, imageError) {
			return true
		}
	}
	return false
}

// buildVisionMessage builds a vision-enabled message with images
func (c *Client) buildVisionMessage(equipment []models.EquipmentForClassification, araTypes []models.ARALevel2Type) models.OpenRouterMessage {
	var content []models.OpenRouterContent

	// Add the main instruction text
	instructionText := c.buildVisionInstructionText(araTypes)
	content = append(content, models.OpenRouterContent{
		Type: "text",
		Text: instructionText,
	})

	// Add equipment with images
	for i, eq := range equipment {
		// Add equipment description
		equipmentText := fmt.Sprintf("\n%d. EQUIPMENT: %s\nDESCRIPTION: %s", i+1, eq.Name, eq.Description)
		content = append(content, models.OpenRouterContent{
			Type: "text",
			Text: equipmentText,
		})

		// Add image if available
		if eq.ImageURL != "" {
			content = append(content, models.OpenRouterContent{
				Type: "image_url",
				ImageURL: &models.OpenRouterImageURL{
					URL:    eq.ImageURL,
					Detail: "high", // Use high detail for better classification
				},
			})
		}
	}

	// Add final instruction
	finalText := "\n\nBased on the equipment descriptions and images above, classify each equipment into the most appropriate ARA Level2 type. Return ONLY a valid JSON array with the exact format specified in the instructions."
	content = append(content, models.OpenRouterContent{
		Type: "text",
		Text: finalText,
	})

	return models.OpenRouterMessage{
		Role:    "user",
		Content: content,
	}
}

// buildVisionInstructionText builds the instruction text for vision-enabled classification
func (c *Client) buildVisionInstructionText(araTypes []models.ARALevel2Type) string {
	instruction := `You are an expert equipment classifier with vision capabilities. You will classify equipment into ARA (Associated Rental Association) categories using both the text descriptions AND the provided images.

ARA LEVEL2 TYPES (with their Level1 parent categories):
`

	for _, araType := range araTypes {
		instruction += fmt.Sprintf("- %s (Level1: %s, Level1_ID: %d, Level2_ID: %d)\n",
			araType.Name, araType.Level1Name, araType.Level1ID, araType.ID)
	}

	instruction += `
INSTRUCTIONS:
1. Analyze BOTH the text description AND the image for each equipment
2. Use the visual information to better understand the equipment's type, size, and function
3. Select the most appropriate ARA Level2 type from the list above
4. Consider the equipment's primary function and industry usage
5. The visual appearance should help distinguish between similar equipment types
6. If uncertain, choose the closest match based on both visual and textual information
7. IMPORTANT: Use ONLY the exact Level2_ID numbers from the list above - do not make up or modify IDs
8. The ara_level1_id will be automatically derived from the Level2 type, so you can set it to 0
9. Return ONLY a valid JSON array with the exact format below

REQUIRED JSON FORMAT:
[
  {"equipment_index": 1, "ara_level1_id": 0, "ara_level2_id": 10600, "confidence": "high|medium|low"},
  {"equipment_index": 2, "ara_level1_id": 0, "ara_level2_id": 11200, "confidence": "high|medium|low"}
]

EQUIPMENT TO CLASSIFY:`

	return instruction
}

// GenerateEquipmentDescription generates bilingual 3-sentence descriptions for equipment using OpenRouter
func (c *Client) GenerateEquipmentDescription(ctx context.Context, equipmentName, imageURL string) (*models.DescriptionGenerationResponse, error) {
	// Generate English description first
	englishDesc, err := c.generateSingleLanguageDescription(ctx, equipmentName, imageURL, "English")
	if err != nil {
		return &models.DescriptionGenerationResponse{
			Success: false,
			Error:   fmt.Sprintf("Failed to generate English description: %v", err),
		}, err
	}

	// Generate French description
	frenchDesc, err := c.generateSingleLanguageDescription(ctx, equipmentName, imageURL, "French")
	if err != nil {
		return &models.DescriptionGenerationResponse{
			Success: false,
			Error:   fmt.Sprintf("Failed to generate French description: %v", err),
		}, err
	}

	return &models.DescriptionGenerationResponse{
		DescriptionEN: englishDesc,
		DescriptionFR: frenchDesc,
		Success:       true,
	}, nil
}

// generateSingleLanguageDescription generates a description in a specific language
func (c *Client) generateSingleLanguageDescription(ctx context.Context, equipmentName, imageURL, language string) (string, error) {
	// Check if we have an image and if the default model supports vision
	hasImage := imageURL != ""
	model := "anthropic/claude-3-haiku" // Default model for description generation
	supportsVision := c.supportsVision(model)

	var message models.OpenRouterMessage
	var useVision bool

	if hasImage && supportsVision {
		message = c.buildDescriptionVisionMessageWithLanguage(equipmentName, imageURL, language)
		useVision = true
	} else {
		prompt := c.buildDescriptionPromptWithLanguage(equipmentName, language)
		message = models.OpenRouterMessage{
			Role:    "user",
			Content: prompt,
		}
		useVision = false
	}

	request := models.OpenRouterRequest{
		Model:       model,
		Temperature: 0.3, // Slightly higher temperature for more creative descriptions
		MaxTokens:   250, // Increased for potential longer French descriptions
		Messages:    []models.OpenRouterMessage{message},
	}

	response, err := c.makeRequest(ctx, request)
	if err != nil {
		// If vision generation failed due to image issues, try text-only fallback
		if useVision && c.isImageRelatedError(err) {
			fmt.Printf("Vision description generation failed due to image issues, falling back to text-only\n")

			// Build text-only message
			prompt := c.buildDescriptionPromptWithLanguage(equipmentName, language)
			textMessage := models.OpenRouterMessage{
				Role:    "user",
				Content: prompt,
			}

			// Retry with text-only
			textRequest := models.OpenRouterRequest{
				Model:       model,
				Temperature: 0.3,
				MaxTokens:   250,
				Messages:    []models.OpenRouterMessage{textMessage},
			}

			response, err = c.makeRequest(ctx, textRequest)
			if err != nil {
				return "", fmt.Errorf("OpenRouter API error (after text fallback): %v", err)
			}
		} else {
			return "", fmt.Errorf("OpenRouter API error: %v", err)
		}
	}

	if response.Error != nil {
		return "", fmt.Errorf("OpenRouter API error: %s", response.Error.Message)
	}

	if len(response.Choices) == 0 {
		return "", fmt.Errorf("no response choices returned from OpenRouter")
	}

	// Extract the description from the response
	content, ok := response.Choices[0].Message.Content.(string)
	if !ok {
		return "", fmt.Errorf("invalid response content type")
	}

	// Clean up the description (remove any extra formatting)
	description := strings.TrimSpace(content)
	return description, nil
}

// GenerateFrenchName generates a French name for equipment using OpenRouter
func (c *Client) GenerateFrenchName(ctx context.Context, englishName string) (*models.FrenchNameGenerationResponse, error) {
	model := "anthropic/claude-3-haiku" // Use fast model for name translation

	prompt := fmt.Sprintf(`You are a professional equipment translator specializing in construction and industrial equipment.

Translate the following English equipment name to French, following these guidelines:
1. Use proper French technical terminology for construction/industrial equipment
2. Maintain the technical accuracy and specificity of the original name
3. Use standard French equipment naming conventions
4. Return ONLY the French name, no explanations or additional text
5. If it's a brand name or model number, keep those parts unchanged but translate descriptive parts

English equipment name: "%s"

French name:`, englishName)

	message := models.OpenRouterMessage{
		Role:    "user",
		Content: prompt,
	}

	request := models.OpenRouterRequest{
		Model:       model,
		Temperature: 0.1, // Low temperature for consistent translations
		MaxTokens:   100, // Short response for just the name
		Messages:    []models.OpenRouterMessage{message},
	}

	response, err := c.makeRequest(ctx, request)
	if err != nil {
		return &models.FrenchNameGenerationResponse{
			Success: false,
			Error:   fmt.Sprintf("OpenRouter API error: %v", err),
		}, err
	}

	if response.Error != nil {
		return &models.FrenchNameGenerationResponse{
			Success: false,
			Error:   fmt.Sprintf("OpenRouter API error: %s", response.Error.Message),
		}, fmt.Errorf("openrouter API error: %s", response.Error.Message)
	}

	if len(response.Choices) == 0 {
		return &models.FrenchNameGenerationResponse{
			Success: false,
			Error:   "No response choices returned from OpenRouter",
		}, fmt.Errorf("no response choices returned from OpenRouter")
	}

	// Extract the French name from the response
	content, ok := response.Choices[0].Message.Content.(string)
	if !ok {
		return &models.FrenchNameGenerationResponse{
			Success: false,
			Error:   "Invalid response content type",
		}, fmt.Errorf("invalid response content type")
	}

	// Clean up the French name (remove any extra formatting or quotes)
	frenchName := strings.TrimSpace(content)
	frenchName = strings.Trim(frenchName, `"'`)

	return &models.FrenchNameGenerationResponse{
		FrenchName: frenchName,
		Success:    true,
	}, nil
}

// buildDescriptionPromptWithLanguage builds a text-only prompt for equipment description generation in a specific language
func (c *Client) buildDescriptionPromptWithLanguage(equipmentName, language string) string {
	if language == "French" {
		return fmt.Sprintf(`Vous êtes un spécialiste expert en équipement. Générez une description professionnelle et précise en 3 phrases pour l'équipement suivant:

Nom de l'équipement: %s

Exigences:
1. Écrivez exactement 3 phrases
2. Concentrez-vous sur la fonction principale de l'équipement et les cas d'utilisation typiques
3. Incluez des détails techniques ou des spécifications pertinents lorsque applicable
4. Utilisez un langage professionnel et clair adapté aux catalogues d'équipement de location
5. N'incluez pas de prix, de disponibilité ou de conditions de location
6. Rendez la description informative et utile pour les locataires potentiels

Veuillez fournir uniquement la description en 3 phrases sans texte ou formatage supplémentaire.`, equipmentName)
	}

	// Default to English
	return fmt.Sprintf(`You are an expert equipment specialist. Generate a professional, accurate 3-sentence description for the following equipment:

Equipment Name: %s

Requirements:
1. Write exactly 3 sentences
2. Focus on the equipment's primary function and typical use cases
3. Include relevant technical details or specifications when applicable
4. Use professional, clear language suitable for rental equipment catalogs
5. Do not include pricing, availability, or rental terms
6. Make the description informative and helpful for potential renters

Please provide only the 3-sentence description without any additional text or formatting.`, equipmentName)
}

// buildDescriptionVisionMessageWithLanguage builds a vision-enabled message for equipment description generation in a specific language
func (c *Client) buildDescriptionVisionMessageWithLanguage(equipmentName, imageURL, language string) models.OpenRouterMessage {
	var content []models.OpenRouterContent

	var instructionText string
	if language == "French" {
		instructionText = fmt.Sprintf(`Vous êtes un spécialiste expert en équipement avec des capacités de vision. Générez une description professionnelle et précise en 3 phrases pour l'équipement suivant en utilisant à la fois le nom et l'image fournie:

Nom de l'équipement: %s

Exigences:
1. Écrivez exactement 3 phrases
2. Analysez l'image pour comprendre l'apparence, la taille et les caractéristiques de l'équipement
3. Concentrez-vous sur la fonction principale de l'équipement et les cas d'utilisation typiques
4. Incluez des détails techniques ou des spécifications pertinents visibles dans l'image
5. Utilisez un langage professionnel et clair adapté aux catalogues d'équipement de location
6. N'incluez pas de prix, de disponibilité ou de conditions de location
7. Rendez la description informative et utile pour les locataires potentiels

Veuillez fournir uniquement la description en 3 phrases sans texte ou formatage supplémentaire.`, equipmentName)
	} else {
		// Default to English
		instructionText = fmt.Sprintf(`You are an expert equipment specialist with vision capabilities. Generate a professional, accurate 3-sentence description for the following equipment using both the name and the provided image:

Equipment Name: %s

Requirements:
1. Write exactly 3 sentences
2. Analyze the image to understand the equipment's appearance, size, and features
3. Focus on the equipment's primary function and typical use cases
4. Include relevant technical details or specifications visible in the image
5. Use professional, clear language suitable for rental equipment catalogs
6. Do not include pricing, availability, or rental terms
7. Make the description informative and helpful for potential renters

Please provide only the 3-sentence description without any additional text or formatting.`, equipmentName)
	}

	content = append(content, models.OpenRouterContent{
		Type: "text",
		Text: instructionText,
	})

	// Add the equipment image using the same approach as ARA classifier
	content = append(content, models.OpenRouterContent{
		Type: "image_url",
		ImageURL: &models.OpenRouterImageURL{
			URL:    imageURL,
			Detail: "high", // Use high detail for better description
		},
	})

	return models.OpenRouterMessage{
		Role:    "user",
		Content: content,
	}
}
