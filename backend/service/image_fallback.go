package service

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"strings"
	"time"

	"cloud.google.com/go/firestore"
	"github.com/vima-inc/derental/models"
)

// ImageFallbackService handles finding alternative images for equipment
type ImageFallbackService struct {
	client *firestore.Client
}

// NewImageFallbackService creates a new image fallback service
func NewImageFallbackService(client *firestore.Client) *ImageFallbackService {
	return &ImageFallbackService{
		client: client,
	}
}

// FindAlternativeImages finds working image URLs for equipment with problematic images
func (s *ImageFallbackService) FindAlternativeImages(ctx context.Context, equipment []models.EquipmentForClassification) ([]models.EquipmentForClassification, error) {
	var updatedEquipment []models.EquipmentForClassification

	// Create a timeout context for the entire operation (max 2 minutes)
	timeoutCtx, cancel := context.WithTimeout(ctx, 2*time.Minute)
	defer cancel()

	for i, eq := range equipment {
		log.Printf("Processing equipment %d/%d: %s", i+1, len(equipment), eq.Name)

		// Check if current image URL is accessible
		if eq.ImageURL != "" && s.isImageAccessible(eq.ImageURL) {
			updatedEquipment = append(updatedEquipment, eq)
			continue
		}

		// Create a timeout context for individual equipment search (max 10 seconds)
		searchCtx, searchCancel := context.WithTimeout(timeoutCtx, 10*time.Second)

		// Find alternative image for this equipment
		alternativeImageURL, err := s.findAlternativeImageURL(searchCtx, eq.Name)
		searchCancel() // Always cancel the search context

		if err != nil {
			log.Printf("Failed to find alternative image for %s: %v", eq.Name, err)
			// Keep original equipment but clear the problematic image URL
			eq.ImageURL = ""
			updatedEquipment = append(updatedEquipment, eq)
			continue
		}

		if alternativeImageURL != "" {
			log.Printf("Found alternative image for %s: %s", eq.Name, alternativeImageURL)
			eq.ImageURL = alternativeImageURL
		} else {
			// No alternative found, clear the image URL for text-only classification
			log.Printf("No alternative image found for %s, using text-only classification", eq.Name)
			eq.ImageURL = ""
		}

		updatedEquipment = append(updatedEquipment, eq)

		// Check if the main context was cancelled
		select {
		case <-timeoutCtx.Done():
			log.Printf("Image fallback operation timed out after processing %d/%d equipment", i+1, len(equipment))
			// Add remaining equipment without images
			for j := i + 1; j < len(equipment); j++ {
				remaining := equipment[j]
				remaining.ImageURL = ""
				updatedEquipment = append(updatedEquipment, remaining)
			}
			return updatedEquipment, nil
		default:
			// Continue processing
		}
	}

	return updatedEquipment, nil
}

// isImageAccessible checks if an image URL is accessible
func (s *ImageFallbackService) isImageAccessible(imageURL string) bool {
	if imageURL == "" {
		return false
	}

	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	resp, err := client.Head(imageURL)
	if err != nil {
		return false
	}
	defer resp.Body.Close()

	return resp.StatusCode == http.StatusOK
}

// findAlternativeImageURL searches for equipment with similar names that have working images
func (s *ImageFallbackService) findAlternativeImageURL(ctx context.Context, equipmentName string) (string, error) {
	// Normalize the equipment name for searching
	normalizedName := s.normalizeEquipmentName(equipmentName)
	keywords := s.extractKeywords(normalizedName)

	if len(keywords) == 0 {
		return "", fmt.Errorf("no keywords extracted from equipment name: %s", equipmentName)
	}

	log.Printf("Searching catalog for alternative image for: %s", equipmentName)

	// Strategy 1: Try to find exact or close matches using the primary keyword
	primaryKeyword := keywords[0] // Use the first keyword as primary
	if imageURL := s.findImageByKeyword(ctx, primaryKeyword, equipmentName); imageURL != "" {
		return imageURL, nil
	}

	// Strategy 2: Try other keywords if primary didn't work
	for i := 1; i < len(keywords) && i < 3; i++ { // Limit to first 3 keywords to avoid timeout
		if imageURL := s.findImageByKeyword(ctx, keywords[i], equipmentName); imageURL != "" {
			return imageURL, nil
		}
	}

	// Strategy 3: Try a broader search with name_en field containing any keyword
	return s.findImageByBroadSearch(ctx, keywords, equipmentName)
}

// findImageByKeyword searches for equipment containing the keyword in name_en field
func (s *ImageFallbackService) findImageByKeyword(ctx context.Context, keyword, originalName string) string {
	log.Printf("Searching for keyword: %s", keyword)

	// First try: Simple query for documents with name_en field that have image_link
	// Use a simpler approach to avoid complex range queries that might need indexes
	query := s.client.Collection("equipments").
		Where("image_link", "!=", "").
		Limit(20) // Limit results to avoid timeout

	iter := query.Documents(ctx)
	defer iter.Stop()

	for {
		doc, err := iter.Next()
		if err != nil {
			if err.Error() == "no more items in iterator" {
				break
			}
			log.Printf("Error querying by keyword %s: %v", keyword, err)
			break
		}

		data := doc.Data()

		// Get equipment name
		nameEN, ok := data["name_en"].(string)
		if !ok || nameEN == "" {
			continue
		}

		// Skip if it's the same equipment
		if strings.EqualFold(nameEN, originalName) {
			continue
		}

		// Check if the name contains the keyword (case-insensitive)
		if !strings.Contains(strings.ToLower(nameEN), strings.ToLower(keyword)) {
			continue
		}

		// Get image URL
		imageURL, ok := data["image_link"].(string)
		if !ok || imageURL == "" {
			continue
		}

		// Check if this image is accessible
		if s.isImageAccessible(imageURL) {
			log.Printf("Found alternative image for %s using keyword %s: %s -> %s", originalName, keyword, nameEN, imageURL)
			return imageURL
		}
	}

	return ""
}

// findImageByBroadSearch performs a broader search when keyword searches fail
func (s *ImageFallbackService) findImageByBroadSearch(ctx context.Context, keywords []string, originalName string) (string, error) {
	log.Printf("Performing broad search for: %s with keywords: %v", originalName, keywords)

	// Query for documents that have image_link field (simpler query)
	query := s.client.Collection("equipments").
		Where("image_link", "!=", "").
		Limit(30) // Reduced limit to avoid timeout

	iter := query.Documents(ctx)
	defer iter.Stop()

	bestMatch := struct {
		imageURL string
		score    int
		name     string
	}{}

	normalizedOriginal := s.normalizeEquipmentName(originalName)
	processedCount := 0

	for {
		doc, err := iter.Next()
		if err != nil {
			if err.Error() == "no more items in iterator" {
				break
			}
			log.Printf("Error in broad search: %v", err)
			break
		}

		processedCount++
		data := doc.Data()

		// Get equipment name
		nameEN, ok := data["name_en"].(string)
		if !ok || nameEN == "" {
			continue
		}

		// Skip if it's the same equipment
		if strings.EqualFold(nameEN, originalName) {
			continue
		}

		// Get image URL
		imageURL, ok := data["image_link"].(string)
		if !ok || imageURL == "" {
			continue
		}

		// Calculate similarity score
		score := s.calculateSimilarityScore(normalizedOriginal, keywords, nameEN)
		if score > bestMatch.score && score > 10 { // Minimum threshold
			// Check if this image is accessible (only for promising candidates)
			if s.isImageAccessible(imageURL) {
				bestMatch.imageURL = imageURL
				bestMatch.score = score
				bestMatch.name = nameEN
				log.Printf("Found potential match for %s: %s (score: %d)", originalName, nameEN, score)

				// If we found a really good match, return early
				if score > 50 {
					log.Printf("Found excellent match, returning early: %s", nameEN)
					break
				}
			}
		}
	}

	log.Printf("Broad search processed %d documents", processedCount)

	if bestMatch.imageURL != "" {
		log.Printf("Selected best match for %s: %s (score: %d)", originalName, bestMatch.name, bestMatch.score)
		return bestMatch.imageURL, nil
	}

	log.Printf("No suitable alternative image found for: %s", originalName)
	return "", nil
}

// normalizeEquipmentName normalizes equipment name for comparison
func (s *ImageFallbackService) normalizeEquipmentName(name string) string {
	// Convert to lowercase and remove extra spaces
	normalized := strings.ToLower(strings.TrimSpace(name))

	// Remove common suffixes/prefixes that might vary
	suffixes := []string{" tool", " equipment", " machine", " device"}
	for _, suffix := range suffixes {
		if strings.HasSuffix(normalized, suffix) {
			normalized = strings.TrimSuffix(normalized, suffix)
			break
		}
	}

	return normalized
}

// extractKeywords extracts important keywords from equipment name
func (s *ImageFallbackService) extractKeywords(name string) []string {
	// Split by common delimiters
	words := strings.FieldsFunc(name, func(c rune) bool {
		return c == ' ' || c == '-' || c == '_' || c == ',' || c == '.'
	})

	// Filter out common stop words
	stopWords := map[string]bool{
		"the": true, "a": true, "an": true, "and": true, "or": true,
		"but": true, "in": true, "on": true, "at": true, "to": true,
		"for": true, "of": true, "with": true, "by": true,
	}

	var keywords []string
	for _, word := range words {
		word = strings.ToLower(strings.TrimSpace(word))
		if len(word) > 2 && !stopWords[word] {
			keywords = append(keywords, word)
		}
	}

	return keywords
}

// calculateSimilarityScore calculates how similar two equipment names are
func (s *ImageFallbackService) calculateSimilarityScore(originalName string, originalKeywords []string, candidateName string) int {
	candidateNormalized := s.normalizeEquipmentName(candidateName)
	candidateKeywords := s.extractKeywords(candidateNormalized)

	score := 0

	// Exact match bonus
	if originalName == candidateNormalized {
		score += 100
	}

	// Keyword matching
	for _, originalKeyword := range originalKeywords {
		for _, candidateKeyword := range candidateKeywords {
			if originalKeyword == candidateKeyword {
				score += 10
			} else if strings.Contains(candidateKeyword, originalKeyword) || strings.Contains(originalKeyword, candidateKeyword) {
				score += 5
			}
		}
	}

	// Substring matching bonus
	if strings.Contains(candidateNormalized, originalName) || strings.Contains(originalName, candidateNormalized) {
		score += 15
	}

	return score
}
