package service

import (
	"context"
	"fmt"
	"reflect"
	"time"

	"github.com/vima-inc/derental/airtable"
	"github.com/vima-inc/derental/models"
)

const masterInventory = "master.inventory"

type equipmentAirtable struct {
	AirtableID               string    `json:"-"`
	ID                       id        `json:"id"`
	NameEN                   any       `json:"Equipment name [EN]"`
	NameFR                   any       `json:"Equipment name [FR]"`
	DescriptionFR            any       `json:"Name description [FR]"`
	DescriptionEN            any       `json:"Name description [EN]"`
	AliasFR                  []string  `json:"Alias [FR]"`
	AliasEN                  []string  `json:"Alias [EN]"`
	Category                 []string  `json:"Category"`
	SubCategory              []string  `json:"Sub Category"`
	Picture                  []picture `json:"Picture"`
	EquipperEquipmentPicture []picture `json:"Picture Proposition"`
	PricePerDay              float64   `json:"Price per day"`
	PricePerWeek             float64   `json:"Price per week"`
	PricePerMonth            float64   `json:"Price per month"`
	Deposit                  float64   `json:"Deposit"`
	WeekendPrice             float64   `json:"Weekend price"`
	Brand                    string    `json:"Brand"`
	BrandModel               string    `json:"Brand model"`
	DriveType                []string  `json:"Drive type"`
	Weight                   string    `json:"Weight (lbs)"`
	Height                   string    `json:"Height (ft)"`
	Width                    string    `json:"Width (ft)"`
	Length                   string    `json:"Length (ft)"`
	Diameter                 string    `json:"Diameter"`
	CutDiameter              string    `json:"Cut diameter"`
	Force                    string    `json:"Force"`
	UsageHours               string    `json:"Usage hours"`
	BTU                      string    `json:"BTU"`
	Volt                     string    `json:"Volt"`
	Watt                     string    `json:"Watt"`
	CFM                      string    `json:"CFM"`
	Capacity                 string    `json:"Capacity"`
	Consumption              string    `json:"Consumption"`
	PropulsionType           []string  `json:"Propulsion type"`
	PlatformHeight           string    `json:"Platform height (ft)"`
	WorkingHeight            string    `json:"Working height (ft)"`
	HorizontalOutreach       string    `json:"Horizontal outreach (ft)"`
	PlatformCapacity         string    `json:"Platform capacity (lbs)"`
	PlatformDimension        string    `json:"Platform dimension (ft)"`
	PlatformExtension        string    `json:"Platform extension"`
	ExtensionCapacity        string    `json:"Extension capacity (lbs)"`
	PlatformRotation         string    `json:"Platform rotation (degree)"`
	MachineRotation          string    `json:"Machine rotation (degree)"`
	MachineWidth             string    `json:"Machine width (ft)"`
	MachineLength            string    `json:"Machine length (ft)"`
	MachineHeight            string    `json:"Machine height (ft)"`
	ClosedMachineHeight      string    `json:"Closed machine height (ft)"`
	ClosedMachineLength      string    `json:"Closed machine length (ft)"`
	ClosedMachineWidth       string    `json:"Closed machine width (ft)"`
	BasketCapacity           string    `json:"Basket capacity"`
	BasketLength             string    `json:"Basket length (ft)"`
	BasketWidth              string    `json:"Basket width (ft)"`
	LegsLocation             string    `json:"Legs location (ft)"`
	FloorHeight              string    `json:"Floor height (ft)"`
	CabinHeight              string    `json:"Cabin height (ft)"`
	Wheelbase                string    `json:"Wheelbase (ft)"`
	WheelSize                string    `json:"Wheel size (in)"`
	PlateDimension           string    `json:"Plate dimension"`
	Decibel                  string    `json:"Decibel"`
	RollWidth                string    `json:"Roll width (ft)"`
	Compaction               string    `json:"Compaction"`
	Vibrations               string    `json:"Vibrations/min."`
	Lumen                    string    `json:"Lumen"`
	Pressure                 string    `json:"Pressure"`
	Frequency                string    `json:"Frequency"`
	TiltingCapacity          string    `json:"Tilting capacity"`
	OperationCapacity        string    `json:"Operation capacity"`
	TankCapacity             string    `json:"Tank capacity"`
	DiggingDepth             string    `json:"Digging depth"`
	DumpingHeight            string    `json:"Dumping height"`
	DiggingRadius            string    `json:"Digging radius"`
	TechnicalDataSheet       string    `json:"Technical data sheet"`
	EquipmentUsages          string    `json:"Usages of equipment"`
}

func (e equipmentAirtable) GetID() string {
	return e.AirtableID
}

func (e *equipmentAirtable) SetID(id string) {
	e.AirtableID = id
}

type inventoryAirtable struct {
	AirtableID    string    `json:"-"`
	ID            id        `json:"id"`
	NameEN        string    `json:"tooler. name [EN]"`
	NameFR        string    `json:"tooler. name [FR]"`
	DescriptionFR string    `json:"Name description [FR]"`
	DescriptionEN string    `json:"Name description [EN]"`
	AliasFR       []string  `json:"Alias [FR]"`
	AliasEN       []string  `json:"Alias [EN]"`
	Category      []string  `json:"Category"`
	SubCategory   []string  `json:"Sub Category"`
	Picture       []picture `json:"Picture"`
}

func (i inventoryAirtable) GetID() string {
	return i.AirtableID
}

func (i *inventoryAirtable) SetID(id string) {
	i.AirtableID = id
}

type id struct {
	Text string `json:"text"`
}

type picture struct {
	URL             string `json:"url"`
	EquipmentNameEN string `json:"filename"`
}

// SyncAirTable syncs Airtable data with the database.
func (s *Service) SyncAirTable(ctx context.Context, equipperID string, currency string) error {
	equipper, err := s.db.GetEquipperByID(ctx, equipperID)
	if err != nil {
		return fmt.Errorf("unable to get equipper: %w", err)
	}

	switch equipper.Company {
	case masterInventory:
		err := s.addToolerInventory(ctx, equipper.Company)
		if err != nil {
			return fmt.Errorf("unable to add tooler inventory: %w", err)
		}

		return nil
	default:
		err := s.addEquipments(ctx, equipper, currency)
		if err != nil {
			return fmt.Errorf("unable to add equipments: %w", err)
		}

		return nil
	}
}

func (s *Service) addEquipments(ctx context.Context, equipper models.Equipper, currency string) error {
	equipments, err := getAirtableEquipment[*equipmentAirtable](ctx, s.airtableAPIKey, equipper.Company)
	if err != nil {
		return fmt.Errorf("unable to get airtable equipments: %w", err)
	}

	equipmentsDB := make([]models.Equipment, 0, len(equipments))

	equipmentsIds := make([]string, 0, len(equipments))

	for _, equipment := range equipments {
		equipmentsDB = append(equipmentsDB, models.Equipment{
			ID:            equipment.GetID() + "_" + equipper.ID + "_" + time.Now().Format("20060102150405"),
			EquipperID:    equipper.ID,
			EquipperName:  equipper.Company,
			EquipperEmail: equipper.Email,
			NameEN:        formatName(equipment.NameEN),
			NameFR:        formatName(equipment.NameFR),
			Description:   formatName(equipment.DescriptionEN),
			DescriptionFR: formatName(equipment.DescriptionFR),
			Alias: models.EquipmentAlias{
				EN: removeEmptyString(equipment.AliasEN),
				FR: removeEmptyString(equipment.AliasFR),
			},
			Price: models.Price{
				Day:      equipment.PricePerDay,
				Week:     equipment.PricePerWeek,
				Month:    equipment.PricePerMonth,
				Currency: currency,
			},
			ImageLink:                getPictureURL(setDefaultImage(equipment.Picture), s.equipmentLibraryURL),
			EquipperEquipmentPicture: getPictureURL(setDefaultImage(equipment.EquipperEquipmentPicture), s.equipmentLibraryURL),
			IsActive:                 true,
			OwnerID:                  equipper.MemberOf,
			AvailableFrom:            time.Now(),
			CreatedAt:                time.Now(),
			UpdatedAt:                time.Now(),
			Category:                 equipment.Category,
			SubCategory:              equipment.SubCategory,
			Status:                   models.EquipmentAvailable,
			//All equipments specs
			InternalID:          equipment.ID.Text,
			Brand:               equipment.Brand,
			BrandModel:          equipment.BrandModel,
			DriveType:           equipment.DriveType,
			Weight:              equipment.Weight,
			Height:              equipment.Height,
			Width:               equipment.Width,
			Length:              equipment.Length,
			Diameter:            equipment.Diameter,
			CutDiameter:         equipment.CutDiameter,
			Force:               equipment.Force,
			UsageHours:          equipment.UsageHours,
			BTU:                 equipment.BTU,
			Volt:                equipment.Volt,
			Watt:                equipment.Watt,
			CFM:                 equipment.CFM,
			Capacity:            equipment.Capacity,
			Consumption:         equipment.Consumption,
			TypeOfPropulsion:    equipment.PropulsionType,
			PlatformHeight:      equipment.PlatformHeight,
			WorkingHeight:       equipment.WorkingHeight,
			HorizontalOutreach:  equipment.HorizontalOutreach,
			PlatformCapacity:    equipment.PlatformCapacity,
			PlatformDimension:   equipment.PlatformDimension,
			PlatformExtension:   equipment.PlatformExtension,
			ExtensionCapacity:   equipment.ExtensionCapacity,
			PlatformRotation:    equipment.PlatformRotation,
			MachineRotation:     equipment.MachineRotation,
			MachineWidth:        equipment.MachineWidth,
			MachineLength:       equipment.MachineLength,
			MachineHeight:       equipment.MachineHeight,
			ClosedMachineHeight: equipment.ClosedMachineHeight,
			ClosedMachineLength: equipment.ClosedMachineLength,
			ClosedMachineWidth:  equipment.ClosedMachineWidth,
			BasketLength:        equipment.BasketLength,
			BasketCapacity:      equipment.BasketCapacity,
			BasketWidth:         equipment.BasketWidth,
			LegsLocation:        equipment.LegsLocation,
			FloorHeight:         equipment.FloorHeight,
			CabinHeight:         equipment.CabinHeight,
			Wheelbase:           equipment.Wheelbase,
			WheelSize:           equipment.WheelSize,
			PlateDimension:      equipment.PlateDimension,
			Decibel:             equipment.Decibel,
			RollWidth:           equipment.RollWidth,
			Compaction:          equipment.Compaction,
			Vibrations:          equipment.Vibrations,
			Lumen:               equipment.Lumen,
			Pressure:            equipment.Pressure,
			Frequency:           equipment.Frequency,
			TiltingCapacity:     equipment.TiltingCapacity,
			OperationCapacity:   equipment.OperationCapacity,
			TankCapacity:        equipment.TankCapacity,
			DiggingDepth:        equipment.DiggingDepth,
			DumpingHeight:       equipment.DumpingHeight,
			DiggingRadius:       equipment.DiggingRadius,
			TechnicalDataSheet:  equipment.TechnicalDataSheet,
			EquipmentUsages:     equipment.EquipmentUsages,
		})

		if equipment.ID.Text != "" {
			equipmentsIds = append(equipmentsIds, equipment.ID.Text)
		}
	}

	res := hasDuplicates(equipmentsIds)

	if res {
		return fmt.Errorf("unable to set equipment with id")
	}

	err = AddEquipments(ctx, s, equipper.ID, "", "", equipmentsDB)
	if err != nil {
		return fmt.Errorf("unable to set equipment with id: %w", err)
	}

	return nil
}

func hasDuplicates(s []string) bool {
	m := make(map[string]bool)

	if len(s) == 0 {
		return false
	}

	for _, element := range s {
		if m[element] {
			return true
		}

		m[element] = true
	}

	return false
}

func (s *Service) addToolerInventory(ctx context.Context, companyName string) error {
	inventoryEquipments, err := getAirtableEquipment[*inventoryAirtable](ctx, s.airtableAPIKey, companyName)
	if err != nil {
		return fmt.Errorf("unable to get airtable equipment: %w", err)
	}

	for _, equipment := range inventoryEquipments {
		inventoryDB := models.ToolerBidzEquipment{
			ID:            equipment.GetID(),
			NameEN:        equipment.NameEN,
			NameFR:        equipment.NameFR,
			DescriptionEN: equipment.DescriptionEN,
			DescriptionFR: equipment.DescriptionFR,
			Category:      equipment.Category,
			SubCategory:   equipment.SubCategory,
			Alias: models.Alias{
				En: removeEmptyString(equipment.AliasEN),
				Fr: removeEmptyString(equipment.AliasFR),
			},
			ImageLink: getPictureURL(setDefaultImage(equipment.Picture), s.equipmentLibraryURL),
		}

		_, err := s.db.AddToolerBidzEquipment(ctx, inventoryDB)
		if err != nil {
			return fmt.Errorf("unable to add tooler bidz equipment: %w", err)
		}
	}

	return nil
}

func getAirtableEquipment[T airtable.FieldType](ctx context.Context, airtableKey, table string) ([]T, error) {
	airtableClient := airtable.New[T](airtableKey, table)

	res, err := airtableClient.GetAll(ctx)
	if err != nil {
		return nil, fmt.Errorf("unable to get equipments from airtable: %w", err)
	}

	return res, nil
}

// If the slice is empty, set the value to the default value.
/*func setDefault(s []string) string {
	if len(s) == 0 {
		return ""
	}

	return s[0]
}*/

func formatName(s any) string {
	if s == nil {
		return ""
	}

	k := reflect.TypeOf(s).Kind()

	if k == reflect.String {
		v, ok := s.(string)
		if ok {
			return v
		}

		return ""
	}

	if k == reflect.Slice {
		v, ok := s.([]any)
		if ok {
			v, ok := v[0].(string)
			if ok {
				return v
			}
		}

		return ""
	}

	return ""
}

func setDefaultImage(s []picture) string {
	if len(s) == 0 {
		return ""
	}

	return s[0].EquipmentNameEN
}
